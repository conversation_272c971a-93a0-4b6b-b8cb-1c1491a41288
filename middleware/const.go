package middleware

/**
 * <AUTHOR>
 * @date 2024/12/19 下午3:55
 */

const (
	ssoAuthErr = `{"msg": "login has error","code": 400, "message": "login has error"}` // 调用sso 服务发生不可控异常默认返回前端

	ssoAddrEnvName     = "SSO_ADDRESS"                   // sso地址
	ssoAuthUriParam    = "uri"                           // query 参数
	ssoAuthMethodParam = "method"                        // query 参数
	ssoAuthPath        = "/console/sso/sdk/cas/userInfo" // 获取sso 登录用户信息

	AccountIdHeader           = "SSO-accountId"           // 账号ID
	UserIdHeader              = "SSO-userId"              // 用户ID
	EmailHeader               = "SSO-email"               // 用户邮箱
	IsRootHeader              = "SSO-isRoot"              // 是否主用户
	NameHeader                = "SSO-name"                // 用户名称
	UserTypeHeader            = "SSO-userType"            // 用户类型(user:普通用户;staff:内部员工;enterprise:企业)，广域网功能使用
	RealNameHeader            = "SSO-realName"            // 实名认证状态(authzed:已认证;noauth:未认证;submitted:已提交;rejected:不通过;human:人工审核)，广域网功能使用
	CityHeader                = "SSO-city"                // 用户所在市公司编码,当渠道为crm时才准确，广域网功能使用
	UidHeader                 = "SSO-uid"                 // 用户id标志（购物车功能使用）
	AgentIdHeader             = "SSO-agentId"             // 代理商ID
	EventTypeHeader           = "SSO-eventType"           // 委托登录类型，1表示被代理登陆（用户通过委托登录的方式登陆，本字段才会存在）
	YunnaoFlagHeader          = "SSO-YUNNAOFLAG"          // 目前没有看到人用
	DelegateHeader            = "SSO-delegate"            // 是否是代理登陆（用户通过委托登录的方式登陆，本字段才会存在）
	DelegateCtyunAcctIdHeader = "SSO-delegateCtyunAcctId" // 代理人的bss的accountId （用户通过委托登录的方式登陆，本字段才会存在）
	DelegateCtyunUserIdHeader = "SSO-delegateCtyunUserId" // 代理人的bss的userId （用户通过委托登录的方式登陆，本字段才会存在）
	DelegateEmailHeader       = "SSO-delegateEmail"       // 代理人的bss的email （用户通过委托登录的方式登陆，本字段才会存在）
	DelegateNameHeader        = "SSO-delegateName"        // 代理人的bss的name （用户通过委托登录的方式登陆，本字段才会存在）
	DelegatePhoneHeader       = "SSO-delegatePhone"       // 代理人的bss的phone （用户通过委托登录的方式登陆，本字段才会存在）

	traceIdKey  = "traceid"
	traceParent = "traceparent"
)
