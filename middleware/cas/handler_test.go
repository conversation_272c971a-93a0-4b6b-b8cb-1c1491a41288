package cas

import (
	"context"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
)

// 记录处理器调用情况的结构体
type handlerRecorder struct {
	successCalled bool
	errorCalled   bool
	lastRequest   *http.Request
	lastResponse  http.ResponseWriter
	lastError     error
	responseBody  string
}

func newHandlerRecorder() *handlerRecorder {
	return &handlerRecorder{}
}

func (h *handlerRecorder) successHandler(w http.ResponseWriter, r *http.Request) {
	h.successCalled = true
	h.lastRequest = r
	h.lastResponse = w
}

func (h *handlerRecorder) errorHandler(w http.ResponseWriter, r *http.Request, err error) {
	h.errorCalled = true
	h.lastRequest = r
	h.lastResponse = w
	h.lastError = err
}

// TestLoginSuccessHandler 测试登录成功处理器
func TestLoginSuccessHandler(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理器记录器
	recorder := newHandlerRecorder()

	// 创建处理器
	handler := NewHandler(config, sessionManager,
		WithSuccessHandler(recorder.successHandler),
		WithErrorHandler(recorder.errorHandler))

	// 获取登录处理函数
	hdr := handler.Login()

	// 使用gomonkey模拟http.Client.do方法，用于模拟CAS服务器的响应
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个成功的响应
			return createTestSuccessResponse()
		})
	defer patch.Reset()

	// 创建请求，包含有效票据
	r := httptest.NewRequest("GET", "http://client.example.com/login?ticket=ST-1234-valid", nil)
	w := httptest.NewRecorder()

	// 处理请求
	hdr(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 对于有效票据，应该重定向到控制台首页
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 应该重定向到控制台首页
	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Client.ConsoleIndex) {
		t.Errorf("Expected location to start with %q, got %q", config.Client.ConsoleIndex, location)
	}

	// 验证成功处理器被调用
	if !recorder.successCalled {
		t.Error("Success handler was not called")
	}

	// 验证错误处理器未被调用
	if recorder.errorCalled {
		t.Error("Error handler was unexpectedly called")
	}
}

// TestLoginErrorHandler 测试登录错误处理器
func TestLoginErrorHandler(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理器记录器
	recorder := newHandlerRecorder()

	// 创建处理器
	handler := NewHandler(config, sessionManager,
		WithSuccessHandler(recorder.successHandler),
		WithErrorHandler(recorder.errorHandler))

	// 获取登录处理函数
	loginHandler := handler.Login()

	// 使用gomonkey模拟http.Client.do方法
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个失败的响应
			return createTestFailureResponse("INVALID_TICKET", "Ticket ST-1234-invalid not recognized")
		})
	defer patch.Reset()

	// 创建请求，包含无效票据
	r := httptest.NewRequest("GET", "http://client.example.com/login?ticket=ST-1234-invalid", nil)
	w := httptest.NewRecorder()

	// 处理请求
	loginHandler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到登录页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 验证错误处理器被调用
	if !recorder.errorCalled {
		t.Error("Error handler was not called")
	}

	// 验证成功处理器未被调用
	if recorder.successCalled {
		t.Error("Success handler was unexpectedly called")
	}

	// 验证错误信息
	if recorder.lastError == nil {
		t.Error("Expected error to be passed to error handler")
	}
}

// TestLogoutSuccessHandler 测试登出成功处理器
func TestLogoutSuccessHandler(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 存储一个测试用户
	ctx := context.Background()
	uuid := "test-logout-uuid"
	user := &LoginUser{
		UUID:       uuid,
		Ticket:     "ST-1234-logout-test",
		Enabled:    true,
		SessionTTL: 3600,
	}
	if err := sessionManager.StoreLoginUser(ctx, user); err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建处理器记录器
	recorder := newHandlerRecorder()

	// 创建处理器
	handler := NewHandler(config, sessionManager,
		WithSuccessHandler(recorder.successHandler),
		WithErrorHandler(recorder.errorHandler))

	// 获取登出处理函数
	logoutHandler := handler.Logout()

	// 创建请求，包含会话Cookie
	r := httptest.NewRequest("GET", "http://client.example.com/logout", nil)
	cookie := &http.Cookie{
		Name:  "consolesessionid",
		Value: uuid,
	}
	r.AddCookie(cookie)
	w := httptest.NewRecorder()

	// 处理请求
	logoutHandler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到CAS服务器的登出页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Server.Host+config.Server.Logout) {
		t.Errorf("Expected Location to start with %q, got %q", config.Server.Host+config.Server.Logout, location)
	}

	// 验证成功处理器被调用
	if !recorder.successCalled {
		t.Error("Success handler was not called")
	}

	// 验证错误处理器未被调用
	if recorder.errorCalled {
		t.Error("Error handler was unexpectedly called")
	}

	// 验证会话被删除
	_, err := sessionManager.GetLoginUser(ctx, uuid)
	if err == nil {
		t.Error("Expected session to be deleted after logout")
	}
}

// TestDelegateSignSuccessHandler 测试委托签名成功处理器
func TestDelegateSignSuccessHandler(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理器记录器
	recorder := newHandlerRecorder()

	// 创建处理器
	handler := NewHandler(config, sessionManager,
		WithSuccessHandler(recorder.successHandler),
		WithErrorHandler(recorder.errorHandler))

	// 获取委托签名处理函数
	delegateHandler := handler.DelegateSign()

	// 创建请求
	r := httptest.NewRequest("GET", "http://client.example.com/delegate?returnUrl=http://app.example.com/callback", nil)
	w := httptest.NewRecorder()

	// 处理请求
	delegateHandler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该成功响应
	if rsp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, rsp.StatusCode)
	}

	// 验证成功处理器被调用
	if !recorder.successCalled {
		t.Error("Success handler was not called")
	}

	// 验证错误处理器未被调用
	if recorder.errorCalled {
		t.Error("Error handler was unexpectedly called")
	}
}

// TestLoginFromAgentSuccessHandler 测试代理登录成功处理器
func TestLoginFromAgentSuccessHandler(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理器记录器
	recorder := newHandlerRecorder()

	// 创建处理器
	handler := NewHandler(config, sessionManager,
		WithSuccessHandler(recorder.successHandler),
		WithErrorHandler(recorder.errorHandler))

	// 获取代理登录处理函数
	agentHandler := handler.LoginFromAgent()

	// 使用gomonkey模拟http.Client.do方法，用于模拟CAS服务器的响应
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个成功的响应
			return createTestSuccessResponse()
		})
	defer patch.Reset()

	// 创建请求，包含代理ID和票据
	r := httptest.NewRequest("GET", "http://client.example.com/loginFromAgent?agentId=test-agent&ticket=ST-1234-agent-test&redirect=http://app.example.com/dashboard", nil)
	w := httptest.NewRecorder()

	// 处理请求
	agentHandler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到指定页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, "http://app.example.com/dashboard") {
		t.Errorf("Expected Location to be redirect URL, got %q", location)
	}

	// 验证成功处理器被调用
	if !recorder.successCalled {
		t.Error("Success handler was not called")
	}

	// 验证错误处理器未被调用
	if recorder.errorCalled {
		t.Error("Error handler was unexpectedly called")
	}
}
