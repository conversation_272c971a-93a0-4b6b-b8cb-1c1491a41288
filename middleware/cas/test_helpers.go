package cas

import (
	"bytes"
	"encoding/xml"
	"io"
	"net/http"
	"strings"
)

// 创建一个标准的测试配置
func createTestConfig() *Config {
	return &Config{
		Client: &ClientConfig{
			Host:                     "http://client.example.com",
			Login:                    "/login",
			AppId:                    "test-app-id",
			AppSecret:                "dGVzdC1hcHAtc2VjcmV0", // base64编码的"test-app-secret"
			ConsoleIndex:             "/console/index",
			DefaultSessionTtlMinutes: 180, // 默认180分钟
		},
		Server: &ServerConfig{
			Host:           "http://auth.example.com",
			Login:          "/login",
			Logout:         "/logout",
			TicketValidate: "/cas/validate",
		},
	}
}

// 创建测试成功响应
func createTestSuccessResponse() (*http.Response, error) {
	// 创建CAS成功验证响应
	casResponse := &struct {
		XMLName     xml.Name `xml:"serviceResponse"`
		Success     bool     `xml:"-"`
		AuthSuccess struct {
			User       string `xml:"user"`
			Attributes struct {
				UserID         string `xml:"userId"`
				AccountID      string `xml:"accountId"`
				Email          string `xml:"email"`
				Name           string `xml:"name"`
				Mobile         string `xml:"mobile"`
				State          string `xml:"state"`
				SessionTTLMins string `xml:"sessionTTLMinutes"`
			} `xml:"attributes"`
		} `xml:"authenticationSuccess"`
	}{
		Success: true,
	}

	casResponse.AuthSuccess.User = "test-user"
	casResponse.AuthSuccess.Attributes.UserID = "test-user-id"
	casResponse.AuthSuccess.Attributes.AccountID = "test-account-id"
	casResponse.AuthSuccess.Attributes.Email = "<EMAIL>"
	casResponse.AuthSuccess.Attributes.Name = "Test User"
	casResponse.AuthSuccess.Attributes.Mobile = "***********"
	casResponse.AuthSuccess.Attributes.State = "ACTIVE"
	casResponse.AuthSuccess.Attributes.SessionTTLMins = "30" // 30分钟会话

	// 序列化为XML
	var xmlBuf bytes.Buffer
	xmlBuf.WriteString(`<?xml version="1.0" encoding="UTF-8"?>`)
	encoder := xml.NewEncoder(&xmlBuf)
	encoder.Indent("", "  ")
	if err := encoder.Encode(casResponse); err != nil {
		return nil, err
	}

	// 创建HTTP响应
	return &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(xmlBuf.String())),
	}, nil
}

// 创建测试失败响应
func createTestFailureResponse(code, message string) (*http.Response, error) {
	// 创建CAS验证失败响应
	casResponse := &struct {
		XMLName  xml.Name `xml:"serviceResponse"`
		Success  bool     `xml:"-"`
		AuthFail struct {
			Code    string `xml:"code,attr"`
			Message string `xml:",chardata"`
		} `xml:"authenticationFailure"`
	}{
		Success: false,
	}

	casResponse.AuthFail.Code = code
	casResponse.AuthFail.Message = message

	// 序列化为XML
	var xmlBuf bytes.Buffer
	xmlBuf.WriteString(`<?xml version="1.0" encoding="UTF-8"?>`)
	encoder := xml.NewEncoder(&xmlBuf)
	encoder.Indent("", "  ")
	if err := encoder.Encode(casResponse); err != nil {
		return nil, err
	}

	// 创建HTTP响应
	return &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(xmlBuf.String())),
	}, nil
}
