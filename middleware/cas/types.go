package cas

import (
	"errors"
	"strings"
)

// ClientConfig CAS客户端配置
type ClientConfig struct {
	// Host 主机地址
	Host string `json:"host"`

	// Login 登录地址
	Login string `json:"login"`

	// AppId cas client appId
	AppId string `json:"appId"`

	// AppSecret cas client appSecret
	AppSecret string `json:"appSecret"`

	// CtyunSessionKeepDuration ctyun的session的时间期限，单位：分钟
	CtyunSessionKeepDuration int64 `json:"ctyunSessionKeepDuration"`

	// DefaultSessionTtlMinutes console的session的默认时间期限，单位：分钟
	DefaultSessionTtlMinutes int64 `json:"defaultSessionTtlMinutes"`

	// ConsoleIndex /console/index
	ConsoleIndex string `json:"consoleIndex"`

	// ConsoleHome /console/home
	ConsoleHome string `json:"consoleHome"`

	// Logout 登出地址
	Logout string `json:"logout"`

	// Debug 环境区分
	Debug bool `json:"debug"`

	// Sp delegateSignSp
	Sp string `json:"sp"`
}

// NewClientConfig 创建新的ClientConfig实例并设置默认值
func NewClientConfig() *ClientConfig {
	return &ClientConfig{
		Host:                     "",
		Login:                    "/login",
		AppId:                    "",
		AppSecret:                "",
		CtyunSessionKeepDuration: 10,
		DefaultSessionTtlMinutes: 180,
		ConsoleIndex:             "/console/index/",
		ConsoleHome:              "/console/home/",
		Logout:                   "/logout",
		Debug:                    false,
		Sp:                       "ctId",
	}
}

// BuildLoginPath 构建登录路径
func (c *ClientConfig) BuildLoginPath() (string, error) {
	if c.Host == "" {
		return "", errors.New("host不能为空")
	}
	if c.Login == "" {
		return "", errors.New("login不能为空")
	}

	// 确保host不以/结尾，login以/开头
	host := strings.TrimSuffix(c.Host, "/")
	login := c.Login
	if !strings.HasPrefix(login, "/") {
		login = "/" + login
	}

	return host + login, nil
}
