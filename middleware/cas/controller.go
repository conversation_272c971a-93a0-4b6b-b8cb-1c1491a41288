package cas

import (
	"net/http"
	"strings"
)

type Controller struct {
	clientConfig *ClientConfig
}

func New() {

}

func Handle(w http.ResponseWriter, r *http.Request) {
	q := r.URL.Query()
	ticket := strings.TrimSpace(q.Get("ticket"))
	logoutRequest := strings.TrimSpace(q.Get("logoutRequest"))
	redirectURL := strings.TrimSpace(q.Get("redirectUrl"))
}

func doLogin(w http.ResponseWriter, r *http.Request, ticket, logoutRequest, directURL string) {
	switch {
	case ticket != "":
	case logoutRequest != "":
	default:
		http.Redirect(w, r, directURL, http.StatusFound)
	}
}

func redirectToLogin()
