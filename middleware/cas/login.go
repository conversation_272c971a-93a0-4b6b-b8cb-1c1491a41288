package cas

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/samber/mo"
	"gopkg.in/cas.v2"
)

// loginHandler
type loginHandler struct {
	*service
}

func LoginHandler(config *Config, sessionManager SessionManager) http.HandlerFunc {
	hdr := &loginHandler{
		service: &service{
			clientConfig:   config.Client,
			serverConfig:   config.Server,
			sessionManager: sessionManager,
			casClient: newClientWrapper(&cas.Options{
				URL: mo.TupleToResult(url.Parse(config.Server.Host)).MustGet(),
			}),
		},
	}
	return hdr.Handle
}

// Handle 处理CAS请求
func (l *loginHandler) Handle(w http.ResponseWriter, r *http.Request) {
	// 处理登录请求
	q := r.URL.Query()
	ticket := strings.TrimSpace(q.Get(queryKeyTicket))
	logoutRequest := strings.TrimSpace(q.Get(queryKeyLogoutRequest))
	redirectURL := strings.TrimSpace(q.Get(queryKeyRedirectUrl))

	err := l.doLogin(w, r, ticket, logoutRequest, redirectURL)
	if err != nil {
		err = errors.Join(err, l.redirectToLogin(w))
	}
	l.handleSuccessOrError(w, r, err)
}

// LoginFromAgentHandler 创建代理登录处理器
func LoginFromAgentHandler(config *Config, sessionManager SessionManager) http.HandlerFunc {
	hdr := &loginHandler{
		service: &service{
			clientConfig:   config.Client,
			serverConfig:   config.Server,
			sessionManager: sessionManager,
			casClient: newClientWrapper(&cas.Options{
				URL: mo.TupleToResult(url.Parse(config.Server.Host)).MustGet(),
			}),
		},
	}
	return hdr.HandleLoginFromAgent
}

// HandleLoginFromAgent 处理代理登录请求
func (l *loginHandler) HandleLoginFromAgent(w http.ResponseWriter, r *http.Request) {
	// 获取请求参数
	q := r.URL.Query()
	agentId := strings.TrimSpace(q.Get(queryKeyAgentID))
	ticket := strings.TrimSpace(q.Get(queryKeyTicket))

	// 检查是否为委托登录
	if agentId != "" && ticket != "" {
		// 委托登录
		err := l.loginFromAgent(w, r, agentId, ticket)
		if err != nil {
			err = errors.Join(err, l.redirectToLogin(w))
		}
		l.handleSuccessOrError(w, r, err)
		return
	}

	// 非委托登录逻辑
	// 从请求中解析UUID
	uuid := parseConsoleSessionUUID(r)

	// 验证用户是否已登录
	loginUser, err := l.validateSessionId(r.Context(), uuid)
	if err == nil && loginUser != nil {
		// 用户已登录，重定向到首页
		l.redirectToIndex(w, "")
		l.handleSuccess(w, r)
		return
	}
	// 用户未登录，重定向到登录页面
	l.handleSuccessOrError(w, r, l.redirectToLogin(w))
}

// doLogin 处理登录逻辑
func (l *loginHandler) doLogin(w http.ResponseWriter, r *http.Request,
	ticket, logoutRequest, redirectUrl string) (err error) {
	switch {
	case ticket != "":
		return l.login(w, r, ticket, redirectUrl)
	case logoutRequest != "":
		return l.logout(w, r, logoutRequest)
	default:
		return errors.New("none ticket nor logoutRequest")
	}
}

// login 使用go-cas库处理登录
func (l *loginHandler) login(w http.ResponseWriter, r *http.Request, ticket, redirectUrl string) (err error) {
	// 构建服务URL
	serviceUrl, err := l.clientConfig.buildLoginPath()
	if err != nil {
		return
	}
	if redirectUrl != "" {
		serviceUrl += "?redirectUrl=" + redirectUrl
	}

	// 验证票据，使用URL编码的服务URL
	encodedServiceUrl := escapeFragment(serviceUrl)

	// 使用validateTicket方法验证票据并创建登录用户
	loginUser, err := l.validateTicket(r.Context(), ticket, encodedServiceUrl, nil)
	if err != nil {
		return
	}

	// 用户为空，重定向到登录页面
	if loginUser == nil {
		return errors.New("login user is nil")
	}

	// 添加控制台Cookie
	l.addConsoleCookie(w, loginUser)

	// 添加CSRF令牌Cookie
	l.addCsrfTokenCookie(r, w)

	// 重定向到首页
	l.redirectToIndex(w, redirectUrl)
	return
}

// loginFromAgent 处理来自代理的登录请求
func (l *loginHandler) loginFromAgent(w http.ResponseWriter, r *http.Request, agentId, ticket string) (err error) {
	// 检查ticket参数
	if ticket == "" {
		return errors.New("ticket is required for agent login")
	}

	// 获取原始查询字符串
	rawQuery := r.URL.RawQuery

	// 构建服务URL，使用完整的查询字符串
	serviceUrl := joinURL(l.clientConfig.Host, "?"+rawQuery)

	// 创建额外字段映射，包含所有相关参数
	extraMap := map[string]any{
		queryKeyAgentID: agentId,
	}

	// 获取额外参数
	if eventTypeValue := r.URL.Query().Get(queryKeyEventType); eventTypeValue != "" {
		extraMap[queryKeyEventType] = eventTypeValue
	}

	if yunnaoFlagValue := r.URL.Query().Get(queryKeyYunnaoFlag); yunnaoFlagValue != "" {
		extraMap[queryKeyYunnaoFlag] = yunnaoFlagValue
	}

	// 使用validateTicket方法验证票据并创建登录用户
	loginUser, err := l.validateTicket(r.Context(), ticket, serviceUrl, extraMap)
	if err != nil {
		return
	}

	// 用户为空，重定向到登录页面
	if loginUser == nil {
		return errors.New("login user is nil")
	}

	// 添加控制台Cookie
	l.addConsoleCookie(w, loginUser)

	// 添加CSRF令牌Cookie
	l.addCsrfTokenCookie(r, w)

	// 获取redirect参数
	redirect := r.URL.Query().Get(queryKeyRedirect)
	if redirect, err = url.QueryUnescape(redirect); err != nil {
		return
	}

	// 处理resourceId参数
	resourceId := r.URL.Query().Get(queryKeyResourceId)
	if resourceId != "" {
		redirect += "?resourceId=" + resourceId
	}

	// 执行重定向
	http.Redirect(w, r, redirect, http.StatusFound)

	return
}

// logout 处理登出请求
func (l *loginHandler) logout(w http.ResponseWriter, r *http.Request, logoutRequest string) (err error) {
	// 如果缺少XML命名空间，则添加
	if !strings.Contains(logoutRequest, "xmlns:samlp=\"urn:oasis:names:tc:SAML:2.0:protocol\"") {
		logoutRequest = strings.ReplaceAll(
			logoutRequest,
			"<samlp:LogoutRequest",
			"<samlp:LogoutRequest xmlns:samlp=\"urn:oasis:names:tc:SAML:2.0:protocol\" xmlns:saml=\"urn:oasis:names:tc:SAML:2.0:assertion\"",
		)
	}

	// 从SessionIndex提取票据
	ticket, _ := parseSAMLLogoutRequest(logoutRequest)
	if ticket != "" {
		// 通过票据删除会话
		err = l.sessionManager.DeleteLoginUserByTicket(r.Context(), ticket)

		// 禁用Cookie
		l.disableConsoleCookie(w)
	}

	// 重定向到登录页面
	return errors.Join(err, l.redirectToLogin(w))
}

// computeSignature 计算HMAC-SHA256签名用于票据验证
func (l *loginHandler) computeSignature(ticket, serviceUrl string, timestamp int64) (signature string, err error) {
	// 创建要签名的字符串: appId@serviceUrl@ticket@timestamp
	toSign := fmt.Sprintf("%s@%s@%s@%d", l.clientConfig.AppId, serviceUrl, ticket, timestamp)

	// 使用base64编码的appSecret作为密钥进行HMAC-SHA256
	key, err := base64.RawURLEncoding.DecodeString(strings.TrimRight(l.clientConfig.AppSecret, "="))
	if err != nil {
		return "", err
	}
	h := hmac.New(sha256.New, key)
	if _, err = h.Write([]byte(toSign)); err != nil {
		return
	}

	// 返回base64编码的签名
	return base64.RawURLEncoding.EncodeToString(h.Sum(nil)), nil
}

// validateTicket 验证票据并创建登录用户
func (l *loginHandler) validateTicket(ctx context.Context, ticket, service string, extraMap map[string]any) (
	user *LoginUser, err error) {
	// 创建自定义验证URL
	timestamp := time.Now().UnixMilli()
	signature, err := l.computeSignature(ticket, service, timestamp)
	if err != nil {
		return
	}

	// 构建完整的验证URL
	validationUrl := joinURL(l.serverConfig.Host, l.serverConfig.TicketValidate)
	customValidationURL := fmt.Sprintf("%s?ticket=%s&service=%s&timestamp=%d&appId=%s&signature=%s",
		validationUrl, url.QueryEscape(ticket), url.QueryEscape(service), timestamp,
		url.QueryEscape(l.clientConfig.AppId), url.QueryEscape(signature))

	// 进行验证
	authRsp, err := l.casClient.validateUrlForServiceTicket(customValidationURL)
	if err != nil {
		return
	}

	// 创建登录用户
	user, err = l.createLoginUser(ctx, ticket, authRsp, extraMap)
	if err != nil {
		return
	}

	return
}

// doLogout 处理主动登出
func (l *loginHandler) doLogout(w http.ResponseWriter, r *http.Request) error {
	// 从请求中解析UUID
	uuid := parseConsoleSessionUUID(r)

	// 如果存在UUID，则删除会话并禁用Cookie
	if uuid != "" {
		_ = l.sessionManager.DeleteLoginUser(r.Context(), uuid)
		l.disableConsoleCookie(w)
	}

	// 构建服务URL（指向控制台首页）
	serviceUrl := joinURL(l.clientConfig.Host, l.clientConfig.ConsoleIndex)

	// 构建登出URL
	newUrl := joinURL(l.serverConfig.Host, l.serverConfig.Logout) + "?service=" + escapeFragment(serviceUrl)

	// 执行重定向 - 使用带请求参数的http.Redirect
	http.Redirect(w, r, newUrl, http.StatusFound)
	return nil
}
