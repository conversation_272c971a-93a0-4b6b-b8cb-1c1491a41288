package cas

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
)

// 测试前准备Redis客户端
func setupRedisClient(t *testing.T) redis.UniversalClient {
	// 创建Redis客户端连接
	redisAddr := "localhost:26379" // 默认地址
	redisPassword := "ci"

	// 从环境变量获取Redis地址（如果有）
	if envAddr := os.Getenv("TEST_REDIS_ADDR"); envAddr != "" {
		redisAddr = envAddr
	}
	if envPassword := os.Getenv("TEST_REDIS_PASSWORD"); envPassword != "" {
		redisPassword = envPassword
	}

	client := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: redisPassword,
	})

	// 测试连接
	ctx := context.Background()
	_, err := client.Ping(ctx).Result()
	if err != nil {
		t.Skipf("跳过Redis测试：无法连接到Redis服务器: %v", err)
		return nil
	}

	return client
}

// TestRedisSessionManager_CRUD 测试Redis会话管理器的CRUD操作
func TestRedisSessionManager_CRUD(t *testing.T) {
	// 设置Redis客户端
	client := setupRedisClient(t)
	if client == nil {
		return
	}
	defer client.Close()

	// 创建Redis会话管理器
	manager, err := NewRedisSessionManager(WithRedisClient(client))
	if err != nil {
		t.Fatalf("创建Redis会话管理器失败: %v", err)
	}

	ctx := context.Background()

	// 清理可能存在的测试数据
	cleanupTestKeys(ctx, t, client)

	// 测试：创建登录用户
	user := &LoginUser{
		UUID:       "redis-test-uuid",
		Ticket:     "ST-redis-********",
		Enabled:    true,
		SessionTTL: 3600,
		AuthUserID: &AuthUserID{
			UserID:    "redis-test-user",
			AccountID: "redis-test-account",
			Email:     "<EMAIL>",
			Name:      "Redis Test User",
		},
	}

	// 存储用户
	err = manager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("StoreLoginUser() error = %v", err)
	}

	// 通过UUID获取用户
	retrievedUser, err := manager.GetLoginUser(ctx, user.UUID)
	if err != nil {
		t.Fatalf("GetLoginUser() error = %v", err)
	}

	// 验证检索到的用户数据
	if retrievedUser.UUID != user.UUID {
		t.Errorf("检索到的用户UUID不匹配, got = %v, want = %v", retrievedUser.UUID, user.UUID)
	}
	if retrievedUser.Ticket != user.Ticket {
		t.Errorf("检索到的用户Ticket不匹配, got = %v, want = %v", retrievedUser.Ticket, user.Ticket)
	}
	if retrievedUser.AuthUserID == nil {
		t.Fatal("检索到的用户AuthUserID为空")
	}
	if retrievedUser.AuthUserID.UserID != user.AuthUserID.UserID {
		t.Errorf("检索到的用户UserID不匹配, got = %v, want = %v", retrievedUser.AuthUserID.UserID, user.AuthUserID.UserID)
	}

	// 测试：通过票据删除用户
	err = manager.DeleteLoginUserByTicket(ctx, user.Ticket)
	if err != nil {
		t.Fatalf("DeleteLoginUserByTicket() error = %v", err)
	}

	// 验证用户已被删除
	_, err = manager.GetLoginUser(ctx, user.UUID)
	if err == nil {
		t.Error("GetLoginUser() error = nil, 期望错误表示用户不存在")
	}

	// 测试：创建新用户并通过UUID删除
	user2 := &LoginUser{
		UUID:       "redis-test-uuid-2",
		Ticket:     "ST-redis-87654321",
		Enabled:    true,
		SessionTTL: 3600,
	}
	err = manager.StoreLoginUser(ctx, user2)
	if err != nil {
		t.Fatalf("StoreLoginUser() error = %v", err)
	}

	// 通过UUID删除用户
	err = manager.DeleteLoginUser(ctx, user2.UUID)
	if err != nil {
		t.Fatalf("DeleteLoginUser() error = %v", err)
	}

	// 验证用户已被删除
	_, err = manager.GetLoginUser(ctx, user2.UUID)
	if err == nil {
		t.Error("GetLoginUser() error = nil, 期望错误表示用户不存在")
	}

	// 清理测试数据
	cleanupTestKeys(ctx, t, client)
}

// 清理测试键
func cleanupTestKeys(ctx context.Context, t *testing.T, client redis.UniversalClient) {
	// 删除用户键
	userKeys, err := client.Keys(ctx, uuidLoginUserPrefix+"redis-test-*").Result()
	if err != nil {
		t.Logf("获取用户键失败: %v", err)
	}
	if len(userKeys) > 0 {
		if err := client.Del(ctx, userKeys...).Err(); err != nil {
			t.Logf("删除用户键失败: %v", err)
		}
	}

	// 删除票据键
	ticketKeys, err := client.Keys(ctx, userTicketUUIDPrefix+"ST-redis-*").Result()
	if err != nil {
		t.Logf("获取票据键失败: %v", err)
	}
	if len(ticketKeys) > 0 {
		if err := client.Del(ctx, ticketKeys...).Err(); err != nil {
			t.Logf("删除票据键失败: %v", err)
		}
	}
}

// TestRedisSessionManager_TTL 测试Redis会话管理器的TTL功能
func TestRedisSessionManager_TTL(t *testing.T) {
	// 设置Redis客户端
	client := setupRedisClient(t)
	if client == nil {
		return
	}
	defer client.Close()

	// 创建Redis会话管理器
	manager, err := NewRedisSessionManager(WithRedisClient(client))
	if err != nil {
		t.Fatalf("创建Redis会话管理器失败: %v", err)
	}

	ctx := context.Background()

	// 清理可能存在的测试数据
	cleanupTestKeys(ctx, t, client)

	// 创建短TTL的用户
	user := &LoginUser{
		UUID:       "redis-ttl-test-uuid",
		Ticket:     "ST-redis-ttl-test",
		Enabled:    true,
		SessionTTL: 2, // 2秒过期
	}

	// 存储用户
	err = manager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("StoreLoginUser() error = %v", err)
	}

	// 立即验证用户可以被检索
	_, err = manager.GetLoginUser(ctx, user.UUID)
	if err != nil {
		t.Fatalf("立即获取用户失败: %v", err)
	}

	// 等待TTL过期
	time.Sleep(3 * time.Second)

	// 验证用户已过期
	_, err = manager.GetLoginUser(ctx, user.UUID)
	if err == nil {
		t.Error("GetLoginUser() error = nil, 期望错误表示用户已过期")
	}

	// 清理测试数据
	cleanupTestKeys(ctx, t, client)
}

// TestRedisSessionManager_RefreshSession 测试Redis会话管理器的刷新会话功能
func TestRedisSessionManager_RefreshSession(t *testing.T) {
	// 设置Redis客户端
	client := setupRedisClient(t)
	if client == nil {
		return
	}
	defer client.Close()

	// 创建Redis会话管理器
	manager, err := NewRedisSessionManager(WithRedisClient(client))
	if err != nil {
		t.Fatalf("创建Redis会话管理器失败: %v", err)
	}

	ctx := context.Background()

	// 清理可能存在的测试数据
	cleanupTestKeys(ctx, t, client)

	// 创建短TTL的用户
	user := &LoginUser{
		UUID:       "redis-refresh-test-uuid",
		Ticket:     "ST-redis-refresh-test",
		Enabled:    true,
		SessionTTL: 2, // 2秒过期
	}

	// 存储用户
	err = manager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("StoreLoginUser() error = %v", err)
	}

	// 等待1秒（TTL的一半）
	time.Sleep(1 * time.Second)

	// 刷新会话，设置更长的TTL
	newTTL := 5 * time.Second
	err = manager.RefreshUserSession(ctx, user.UUID, user, newTTL)
	if err != nil {
		t.Fatalf("RefreshUserSession() error = %v", err)
	}

	// 再等待2秒（原始TTL已过期，但新TTL未过期）
	time.Sleep(2 * time.Second)

	// 验证用户仍然可以被检索
	refreshedUser, err := manager.GetLoginUser(ctx, user.UUID)
	if err != nil {
		t.Fatalf("刷新后获取用户失败: %v", err)
	}

	if refreshedUser == nil {
		t.Fatal("刷新后用户不应为nil")
	}

	// 验证用户数据保持不变
	if refreshedUser.UUID != user.UUID {
		t.Errorf("刷新后用户UUID不匹配, got = %v, want = %v", refreshedUser.UUID, user.UUID)
	}
	if refreshedUser.Ticket != user.Ticket {
		t.Errorf("刷新后用户Ticket不匹配, got = %v, want = %v", refreshedUser.Ticket, user.Ticket)
	}

	// 等待剩余的刷新TTL过期
	time.Sleep(3 * time.Second)

	// 验证用户现在已过期
	_, err = manager.GetLoginUser(ctx, user.UUID)
	if err == nil {
		t.Error("GetLoginUser() error = nil, 期望错误表示刷新后的用户已过期")
	}

	// 清理测试数据
	cleanupTestKeys(ctx, t, client)
}
