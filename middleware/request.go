package middleware

import (
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"net/url"
	"time"
)

/**
 * <AUTHOR>
 * @date 2024/12/19 下午3:58
 */

var ssoClient = &http.Client{
	Timeout: time.Second * 5,
}

var ssoNeedCookieList = []string{
	"consolesessionid",
	"ct_tgc",
	"console_user_id",
}

func GetSsoUserInfo(ssoAddr string, ctApiRequest *CtApiRequest, r *http.Request) (*SsoResponse, error) {
	if len(ssoAddr) == 0 {
		return nil, errors.New("sso address is empty")
	}

	var ssoAuthReqParam = url.Values{
		ssoAuthUriParam:    {r.RequestURI},
		ssoAuthMethodParam: {r.Method},
	}

	var ssoAuthUrl = ssoAddr + ssoAuthPath

	var ssoResp *http.Response
	var err error
	header := make(http.Header)
	for _, k := range ssoNeedCookieList {
		cookie, _ := r.<PERSON>ie(k)
		var v string
		if cookie != nil {
			v = cookie.Value
		}
		header.Set(k, v)
	}

	traceId := r.Header.Get(traceIdKey)
	if len(traceId) > 0 {
		header.Set(traceIdKey, traceId)
	}
	tp := r.Header.Get(traceParent)
	if len(tp) > 0 {
		header.Set(traceParent, tp)
	}

	if ctApiRequest == nil {
		ssoAuthReq, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("%s?%s", ssoAuthUrl, ssoAuthReqParam.Encode()), nil)
		ssoAuthReq.Header = header
		ssoResp, err = ssoClient.Do(ssoAuthReq)
	} else {
		ssoResp, err = ctApiRequest.Get(r.Context(), ssoAuthUrl, ssoAuthReqParam.Encode(), header, time.Second*10)
	}

	if err != nil {
		return nil, errors.Wrap(err, "sso auth request err")
	}

	defer func() {
		_ = ssoResp.Body.Close()
	}()
	var ssoRet SsoResponse
	ssoRespBody, _ := io.ReadAll(ssoResp.Body)
	if err = json.Unmarshal(ssoRespBody, &ssoRet); err != nil {
		return nil, errors.Wrap(err, "sso auth unmarshal response err")
	}
	return &ssoRet, nil
}
