package middleware

import (
	"context"
	"encoding/json"
	"net/http"
)

/**
 * <AUTHOR>
 * @date 2024/12/19 下午3:43
 */

// ErrorHandler sso服务发生异常情况，比如调用超时等，业务方可以自定义返回给前端的body，如果没有处理
// 则默认返回 ssoAuthErr
type ErrorHandler func(err error) (body map[string]interface{})

// SuccessHandler 用户校验登录通过。用户可以自定义把用户信息按照自己的
// 方式设置用户信息在上下文
type SuccessHandler func(request *http.Request, ssoResp *SsoInfo) *http.Request

type userInfoKey int

var (

	// 默认处理用户登录校验通过的情况
	defaultSuccessHandler = func(request *http.Request, ssoInfo *SsoInfo) *http.Request {
		setSsoUserHeader(request, ssoInfo)
		return request
	}

	SsoUserInfo userInfoKey

	// UserInfoContextHandler 设置用户信息到context
	UserInfoContextHandler = func(request *http.Request, ssoInfo *SsoInfo) *http.Request {
		if ssoInfo == nil {
			return request
		}
		return request.WithContext(context.WithValue(request.Context(), SsoUserInfo, ssoInfo))
	}
)

// 默认处理用户未登录的情况
var defaultErrorHandler = func(err error) (body map[string]interface{}) {
	var defaultErrorMessage = map[string]interface{}{
		"msg":     "login has error",
		"code":    400,
		"message": "login has error"}
	if err == nil {
		return defaultErrorMessage
	}
	defaultErrorMessage["message"] = err.Error()
	return defaultErrorMessage
}

// 设置用户信息到header
func setSsoUserHeader(r *http.Request, user *SsoInfo) {
	r.Header.Set(AccountIdHeader, user.CtAccountId)
	r.Header.Set(UserIdHeader, user.CtUserId)
	r.Header.Set(EmailHeader, user.Email)
	r.Header.Set(IsRootHeader, user.IsRoot)
	r.Header.Set(NameHeader, user.Username)
	r.Header.Set(UserTypeHeader, user.UserType)
	r.Header.Set(RealNameHeader, user.RealName)
	r.Header.Set(CityHeader, user.City)
	r.Header.Set(UidHeader, user.Uid)
	r.Header.Set(AgentIdHeader, user.AgentId)
	r.Header.Set(EventTypeHeader, user.EventType)
	r.Header.Set(YunnaoFlagHeader, user.YunnaoFlag)
	r.Header.Set(DelegateHeader, user.Delegate)
	r.Header.Set(DelegateCtyunAcctIdHeader, user.DelegateCtyunAcctId)
	r.Header.Set(DelegateCtyunUserIdHeader, user.DelegateCtyunUserId)
	r.Header.Set(DelegateEmailHeader, user.DelegateEmail)
	r.Header.Set(DelegateNameHeader, user.DelegateName)
	r.Header.Set(DelegatePhoneHeader, user.DelegatePhone)
}

func handlerNotLogin(w http.ResponseWriter, ret *SsoResponse) {
	if ret.ReturnObj.RedirectCode == http.StatusFound {
		w.Header().Set("Location", ret.ReturnObj.RedirectUri)
		w.WriteHeader(http.StatusFound)
		return
	}

	w.WriteHeader(http.StatusUnauthorized)
	_, _ = w.Write([]byte(ret.ReturnObj.RedirectUri))
}

func handlerSsoError(w http.ResponseWriter, handlerBody map[string]interface{}) {
	w.WriteHeader(http.StatusOK)
	var errorBodyJson = []byte(ssoAuthErr)
	if len(handlerBody) > 0 {
		errorBodyJson, _ = json.Marshal(handlerBody)
	}
	_, _ = w.Write(errorBodyJson)
}
