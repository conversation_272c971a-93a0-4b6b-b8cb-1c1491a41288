package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type Option func(*SsoAuthMiddleware)

type SsoAuthMiddleware struct {
	ssoAddressHook func() string
	ctApiRequest   *CtApiRequest
	successHandler SuccessHandler
	errorHandler   ErrorHandler
}

// WithSuccessHandler 用户校验登录通过回调的handler。可以自定义把用户信息设置在习惯的地方
func WithSuccessHandler(handler SuccessHandler) Option {
	return func(m *SsoAuthMiddleware) {
		m.successHandler = handler
	}
}

// WithErrorHandler  sso服务发生异常情况回调handler，比如调用超时等，业务方可以自定义返回给前端的body，如果没有处理使用
// 默认 ssoAuthErr
func WithErrorHandler(handler ErrorHandler) Option {
	return func(m *SsoAuthMiddleware) {
		m.errorHandler = handler
	}
}

// WithSsoAddress 获取sso服务地址
func WithSsoAddress(addressFunc func() string) Option {
	return func(m *SsoAuthMiddleware) {
		m.ssoAddressHook = addressFunc
	}
}

// WithCtApiSign 获取ctapi接口所需的ak、sk；从eop接口调用sso服务时，需要传入
func WithCtApiSign(ctApiRequest *CtApiRequest) Option {
	return func(m *SsoAuthMiddleware) {
		m.ctApiRequest = ctApiRequest
	}
}

func NewSsoAuthMiddleware(opts ...Option) *SsoAuthMiddleware {
	if len(opts) == 0 {
		panic("NewSsoAuthMiddleware opts is empty, please check config")
	}

	middleware := &SsoAuthMiddleware{
		successHandler: defaultSuccessHandler,
		errorHandler:   defaultErrorHandler,
	}

	for _, opt := range opts {
		opt(middleware)
	}

	if middleware.ssoAddressHook == nil {
		panic("NewSsoAuthMiddleware WithSsoAddress is nil, please check config")
	}

	return middleware
}

// HandleGin 获取gin web框架的中间件
func (s *SsoAuthMiddleware) HandleGin() gin.HandlerFunc {
	return func(c *gin.Context) {
		rsp, ok := s.handle(c.Writer, c.Request)
		if !ok {
			c.Abort()
			return
		}
		c.Request = s.successHandler(c.Request, rsp.ReturnObj)
		c.Next()
	}
}

// HandleHttp 获取http web框架的中间件.比如go-zero
func (s *SsoAuthMiddleware) HandleHttp(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		rsp, ok := s.handle(w, r)
		if !ok {
			return
		}
		next(w, s.successHandler(r, rsp.ReturnObj))
	}
}

func (s *SsoAuthMiddleware) handle(w http.ResponseWriter, r *http.Request) (rsp *SsoResponse, ok bool) {
	rsp, err := GetSsoUserInfo(s.ssoAddressHook(), s.ctApiRequest, r)
	if err != nil {
		handlerSsoError(w, s.errorHandler(err))
		return
	} else if rsp == nil {
		handlerSsoError(w, s.errorHandler(errors.New("sso service error,return is nil")))
		return
	} else if rsp.ReturnObj == nil {
		handlerSsoError(w, s.errorHandler(errors.New(rsp.Message)))
		return
	}
	if rsp.StatusCode != 800 {
		handlerNotLogin(w, rsp)
		return
	}

	ok = true
	s.successHandler(r, rsp.ReturnObj)
	return
}
