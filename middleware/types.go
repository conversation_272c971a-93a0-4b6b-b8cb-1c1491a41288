package middleware

type SsoResponse struct {
	StatusCode int      `json:"statusCode"`
	ReturnObj  *SsoInfo `json:"returnObj"`
	Error      string   `json:"error"`
	Message    string   `json:"message"`
	TraceId    string   `json:"traceid"`
}

type SsoInfo struct {
	RedirectUri         string                 `json:"redirectUri"` // 跳转的uri,鉴权失败的时候，才会有值
	RedirectCode        int                    `json:"redirectCode"`
	CtUserId            string                 `json:"ctUserId"`    // 天翼云用户ID
	CtAccountId         string                 `json:"ctAccountId"` // 天翼云账号id
	IsRoot              string                 `json:"isRoot"`      // 是否主主账号1-主用户
	Email               string                 `json:"email"`       // 登录天翼云用户邮箱
	Uid                 string                 `json:"uid"`         // 用户id标志（购物车功能使用）
	UserType            string                 `json:"userType"`    // 用户类型(user:普通用户;staff:内部员工;enterprise:企业)，广域网功能使用
	RealName            string                 `json:"realName"`    // 实名认证状态(authzed:已认证;noauth:未认证;submitted:已提交;rejected:不通过;human:人工审核)，广域网功能使用
	City                string                 `json:"city"`        // 用户所在市公司,当渠道为crm时才准确，广域网功能使用
	Username            string                 `json:"username"`    // 用户名称
	AgentId             string                 `json:"agentId"`     // 代理商ID
	EventType           string                 `json:"eventType"`   // 委托登录类型，1表示被代理登陆（用户通过委托登录的方式登陆，本字段才会存在）
	YunnaoFlag          string                 `json:"yunnaoFlag"`
	Delegate            string                 `json:"delegate"`
	DelegateCtyunAcctId string                 `json:"delegateCtyunUAcctId"` // 代理人的bss的accountId （用户通过委托登录的方式登陆，本字段才会存在）
	DelegateCtyunUserId string                 `json:"delegateCtyunUserId"`  // 代理人的bss的userId （用户通过委托登录的方式登陆，本字段才会存在）
	DelegateEmail       string                 `json:"delegateEmail"`        // 代理人的bss的email （用户通过委托登录的方式登陆，本字段才会存在）
	DelegateName        string                 `json:"delegateName"`         // 代理人的bss的电话名称 （用户通过委托登录的方式登陆，本字段才会存在）
	DelegateType        string                 `json:"delegateType"`         // 代理人的bss的类型（用户通过委托登录的方式登陆，本字段才会存在）
	DelegatePhone       string                 `json:"delegatePhone"`        // 代理人的bss的phone（用户通过委托登录的方式登陆，本字段才会存在）
	Extra               map[string]interface{} `json:"extra"`                // 扩展信息，目前没有内容，为了后续扩展
}
