### 接入准备

- 添加go env
```sh
go env -w GOPROXY="https://goproxy.cn,direct"
go env -w GO111MODULE=on
go env -w GOPRIVATE="work.ctyun.cn/*"
go env -w GONOSUMDB="work.ctyun.cn/*"
git config --global url."ssh://*****************:30222".insteadOf "https://work.ctyun.cn/git"
```
- 安装SDK
```sh
go get -u work.ctyun.cn/git/console-common/sso-go-sdk
```

### SSO Middleware 接入

#### sso middleware go-zero web项目接入

```go
import (
    "fmt"
    "net/http"

    "github.com/zeromicro/go-zero/rest"
    "github.com/zeromicro/go-zero/rest/httpx"
    "work.ctyun.cn/git/console-common/sso-go-sdk/middleware"
)

/**
 * <AUTHOR>
 * @date 2024/12/25 上午10:28
 */

func main() {
    // 获取sso服务配置的地址，传递func到sso 中间件，这里使用的时候，自己可以在自己应用配置文件获取，如果接入了
    // 配置中心也可以从配置中心获取(也是本地内存获取)
    addressF := func() string {
        return "http://*************:31161"
    }
    // 1、设置option
    opts := []middleware.Option{
        middleware.WithSsoAddress(addressF),
        // 2、内置的用户登录成功的情况调用successHandler设置用户信息在上下文。默认是设置在header
        // middleware.UserInfoContextHandler 设置在context
        middleware.WithSuccessHandler(middleware.UserInfoContextHandler),
    }
    server := rest.MustNewServer(rest.RestConf{
        Host: "localhost",
        Port: 8080,
    })
    defer server.Stop()
    // 3、sso 中间件设置使用HandleHttp方法
    server.AddRoutes(rest.WithMiddleware(middleware.NewSsoAuthMiddleware(opts...).HandleHttp,
        []rest.Route{
            {
                Method:  http.MethodGet,
                Path:    "/test",
                Handler: myHandler,
            },
        }...))
    server.Start()
}

func myHandler(w http.ResponseWriter, r *http.Request) {
    // 4、提供用了middleware.UserInfoContextHandler,可以按照如下获取用户信息
    fmt.Println(r.Context().Value(middleware.SsoUserInfo).(*middleware.SsoInfo))
    httpx.OkJson(w, map[string]string{"message": "Success"})
}
```

#### sso middleware go-gin web项目接入

```go
import (
    "github.com/gin-gonic/gin"
    "net/http"
    "work.ctyun.cn/git/console-common/sso-go-sdk/middleware"
)

func main() {
    router := gin.Default()
    // 获取sso服务配置的地址，传递func到sso 中间件，这里使用的时候，自己可以在自己应用配置文件获取，如果接入了
    // 配置中心也可以从配置中心获取(也是本地内存获取)
    addressF := func() string {
        return "http://*************:31161"
    }
    // 1、设置option
    opts := []middleware.Option{
        middleware.WithSsoAddress(addressF),
        // 2、内置的用户登录成功的情况调用successHandler设置用户信息在上下文。默认是设置在header
        // middleware.UserInfoContextHandler 设置在context
        middleware.WithSuccessHandler(middleware.UserInfoContextHandler),
    }
    // 3、获取gin 中间件HandleGin()
    router.Use(middleware.NewSsoAuthMiddleware(opts...).HandleGin())
    router.GET("/test", func(c *gin.Context) {
        // 4、提供用了middleware.UserInfoContextHandler,可以按照如下获取用户信息
        fmt.Println(c.Request.Context().Value(middleware.SsoUserInfo).(*middleware.SsoInfo))
        c.PureJSON(http.StatusOK, "test")
    })
    _ = router.Run("127.0.0.1:8888")
}
```

### CAS 登录、登出、委托签名、代理登录 handler 接入

```go
import (
	"log"
	"net/http"
	"work.ctyun.cn/git/console-common/sso-go-sdk/xcas"
)

func main() {
	// 创建CAS配置
	config := &xcas.Config{
		Client: &xcas.ClientConfig{
			Host:                     "https://www.ctyun.cn",
			Login:                    "/login",
			AppId:                    "your-app-id",
			AppSecret:                "your-app-secret",
			DefaultSessionTtlMinutes: 180, // 3小时
			ConsoleIndex:             "/console/index/",
			Sp:                       "ctId",
		},
		Server: &xcas.ServerConfig{
			Host:           "https://console.ctyun.cn",
			Login:          "/cas/login",
			TicketValidate: "/cas/serviceValidate",
			Logout:         "/cas/logout",
		},
	}

	// 创建会话管理器（可选择内存或Redis）
	// 使用内存会话管理器
	sessionManager := xcas.NewMemorySessionManager(1024 * 1024) // 1MB缓存

	// 或者使用Redis会话管理器
	// redisClient := redis.NewClient(&redis.Options{
	//     Addr:     "localhost:6379",
	//     Password: "",
	//     DB:       0,
	// })
	// sessionManager, err := xcas.NewRedisSessionManager(xcas.WithRedisClient(redisClient))
	// if err != nil {
	//     log.Fatal("创建Redis会话管理器失败:", err)
	// }

	// 创建CAS处理器
	handler := xcas.NewHandler(config, sessionManager,
		// 在 WithSuccessHandler 和 WithErrorHandler 处理需要注意以下问题:
		// response 因为已经塞入了 302 重定向, 改写 response 可能无法生效并且会报错
		
		// 可选：自定义成功处理器, 若对请求结果无特殊处理可以不加
        xcas.WithSuccessHandler(func(w http.ResponseWriter, r *http.Request) {
			log.Println("CAS操作成功")
		}),
		// 可选：自定义错误处理器, 若对请求结果无特殊处理可以不加
        xcas.WithErrorHandler(func(w http.ResponseWriter, r *http.Request, err error) {
			// 对于 Login 和 DelegateSign 来说 handler 会给 response 填入 302 重定向到登录页
			log.Printf("CAS错误: %v", err)
		}),
	)

	// 注册路由, 若是使用 gin 或者 go-zero 可以把 handler.Login() 再套一层封装
	// go-gin 可以使用 gin.WrapF(handler.Login())
	
	// 处理 IT ticket&redirectUrl&logoutRequest 请求
	http.HandleFunc("/login", handler.Login())

	// 处理登出请求, 清理 cookie 和缓存
	http.HandleFunc("/logout", handler.Logout())

	// 当从 bcp 代理登录时, 前端需要通过委托签名来换取 ctId 从而获取到被委托人的资源池视图
	http.HandleFunc("/sign", handler.DelegateSign())

	// 委托登录, 处理 agentId&ticket&redirectUrl 请求, 即来自 bcp 代理登录的请求
	http.HandleFunc("/", handler.LoginFromAgent())

	// 启动服务器
	log.Println("服务器启动在 :8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}
```

### CAS 登入后 context 中获取用户信息 middleware 接入

#### Gin 框架中间件

```go
import (
	"net/http"
	"github.com/gin-gonic/gin"
	"work.ctyun.cn/git/console-common/sso-go-sdk/xcas"
)

func main() {
	// 创建会话管理器, 生产环境请使用 xcas.NewRedisSessionManager
	sessionManager := xcas.NewMemorySessionManager(1024 * 1024)

	// 创建认证中间件，WithUnauthorizedHandler 为可选项, 默认逻辑为: 无论是否有授权都会继续处理请求
	casMiddleware := xcas.NewMiddleware(sessionManager).
		WithUnauthorizedHandler(func(w http.ResponseWriter, r *http.Request, err error) bool {
            // 返回false表示停止处理请求，返回true表示继续处理
            if err != nil {
                log.Printf("认证失败: %v", err)
            }
            http.Error(w, "未授权访问", http.StatusUnauthorized)
            return false // 停止处理请求
		})

	// 创建Gin路由
	r := gin.Default()

	// 使用CAS填充用户信息的中间件
	r.Use(casMiddleware.ForGin())

	// 定义受保护的路由
	r.GET("/user-info", func(c *gin.Context) {
		// 从Gin context中获取用户信息
		user := xcas.GetLoginUserFromGin(c)
		if user != nil {
			c.JSON(http.StatusOK, gin.H{
				"message":   "获取用户信息成功",
				"userId":    user.AuthUserID.UserID,
				"userName":  user.AuthUserID.Name,
				"email":     user.AuthUserID.Email,
				"accountId": user.AuthUserID.AccountID,
				"isRoot":    user.AuthUserID.IsRoot,
				"userType":  user.AuthUserID.UserType,
				"realName":  user.AuthUserID.RealName,
				"city":      user.AuthUserID.City,
			})
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "未找到用户信息",
			})
		}
	})

	r.Run(":8080")
}
```

#### Go-Zero 框架中间件

```go
import (
	"errors"
	"fmt"
	"net/http"

	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
	"work.ctyun.cn/git/console-common/sso-go-sdk/middleware/xcas"
)

func main() {
	// 创建会话管理器, 生产环境请使用 xcas.NewRedisSessionManager
	sessionManager := xcas.NewMemorySessionManager(1024 * 1024)

	// 创建CAS认证中间件，WithUnauthorizedHandler 为可选项, 默认逻辑为: 无论是否有授权都会继续处理请求
	casMiddleware := xcas.NewMiddleware(sessionManager).
		WithUnauthorizedHandler(func(w http.ResponseWriter, r *http.Request, err error) bool {
            // 返回false表示停止处理请求，返回true表示继续处理
			http.Error(w, "未授权访问", http.StatusUnauthorized)
			return false
		})

	server := rest.MustNewServer(rest.RestConf{
		Host: "localhost",
		Port: 8080,
	})
	defer server.Stop()

	// 使用CAS中间件填充用户信息
	server.AddRoutes(rest.WithMiddleware(casMiddleware.Handler,
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/user-info",
				Handler: getUserInfoHandler,
			},
		}...))

	server.Start()
}

func getUserInfoHandler(w http.ResponseWriter, r *http.Request) {
	// 从context中获取用户信息
	user := xcas.GetLoginUser(r)
	if user != nil {
		httpx.OkJson(w, map[string]interface{}{
			"userId":    user.AuthUserID.UserID,
			"userName":  user.AuthUserID.Name,
			"email":     user.AuthUserID.Email,
			"accountId": user.AuthUserID.AccountID,
			"isRoot":    user.AuthUserID.IsRoot,
			"userType":  user.AuthUserID.UserType,
			"realName":  user.AuthUserID.RealName,
			"city":      user.AuthUserID.City,
		})
	} else {
		httpx.Error(w, errors.New("未找到用户信息"))
	}
}
```
