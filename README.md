- 添加go env
```sh
go env -w GOPROXY="https://goproxy.cn,direct"
go env -w GO111MODULE=on
go env -w GOPRIVATE="work.ctyun.cn/*"
go env -w GONOSUMDB="work.ctyun.cn/*"
git config --global url."ssh://*****************:30222".insteadOf "https://work.ctyun.cn/git"
```
- 安装SDK
```sh
go get -u work.ctyun.cn/git/console-common/sso-go-sdk
```

- sso middleware go-zero web项目接入
```go
import (
    "fmt"
    "net/http"

    "github.com/zeromicro/go-zero/rest"
    "github.com/zeromicro/go-zero/rest/httpx"
    "work.ctyun.cn/git/console-common/sso-go-sdk/middleware"
)

/**
 * <AUTHOR>
 * @date 2024/12/25 上午10:28
 */

func main() {
    // 获取sso服务配置的地址，传递func到sso 中间件，这里使用的时候，自己可以在自己应用配置文件获取，如果接入了
    // 配置中心也可以从配置中心获取(也是本地内存获取)
    addressF := func() string {
        return "http://*************:31161"
    }
    // 1、设置option
    opts := []middleware.Option{
        middleware.WithSsoAddress(addressF),
        // 2、内置的用户登录成功的情况调用successHandler设置用户信息在上下文。默认是设置在header
        // middleware.UserInfoContextHandler 设置在context
        middleware.WithSuccessHandler(middleware.UserInfoContextHandler),
    }
    server := rest.MustNewServer(rest.RestConf{
        Host: "localhost",
        Port: 8080,
    })
    defer server.Stop()
    // 3、sso 中间件设置使用HandleHttp方法
    server.AddRoutes(rest.WithMiddleware(middleware.NewSsoAuthMiddleware(opts...).HandleHttp,
        []rest.Route{
            {
                Method:  http.MethodGet,
                Path:    "/test",
                Handler: myHandler,
            },
        }...))
    server.Start()
}

func myHandler(w http.ResponseWriter, r *http.Request) {
    // 4、提供用了middleware.UserInfoContextHandler,可以按照如下获取用户信息
    fmt.Println(r.Context().Value(middleware.SsoUserInfo).(*middleware.SsoInfo))
    httpx.OkJson(w, map[string]string{"message": "Success"})
}
```

- sso middleware go-gin web项目接入
```go
import (
    "github.com/gin-gonic/gin"
    "net/http"
    "work.ctyun.cn/git/console-common/sso-go-sdk/middleware"
)

func main() {
    router := gin.Default()
    // 获取sso服务配置的地址，传递func到sso 中间件，这里使用的时候，自己可以在自己应用配置文件获取，如果接入了
    // 配置中心也可以从配置中心获取(也是本地内存获取)
    addressF := func() string {
        return "http://*************:31161"
    }
    // 1、设置option
    opts := []middleware.Option{
        middleware.WithSsoAddress(addressF),
        // 2、内置的用户登录成功的情况调用successHandler设置用户信息在上下文。默认是设置在header
        // middleware.UserInfoContextHandler 设置在context
        middleware.WithSuccessHandler(middleware.UserInfoContextHandler),
    }
    // 3、获取gin 中间件HandleGin()
    router.Use(middleware.NewSsoAuthMiddleware(opts...).HandleGin())
    router.GET("/test", func(c *gin.Context) {
        // 4、提供用了middleware.UserInfoContextHandler,可以按照如下获取用户信息
        fmt.Println(c.Request.Context().Value(middleware.SsoUserInfo).(*middleware.SsoInfo))
        c.PureJSON(http.StatusOK, "test")
    })
    _ = router.Run("127.0.0.1:8888")
}
```

- python3
```python
# -*- coding:utf-8 -*-

import json
import logging
import requests
from django.http import JsonResponse, HttpResponseRedirect
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin

# 默认的 SSO 地址和处理函数
DEFAULT_SSO_ADDRESS = settings.SSO_ADDRESS  # 假设在 settings.py 中配置了 SSO 地址
DEFAULT_SUCCESS_HANDLER = None
DEFAULT_ERROR_HANDLER = None

ssoNeedCookieList = ['consolesessionid', 'ct_tgc', 'console_user_id']

# 日志配置
logger = logging.getLogger(__name__)


# 定义 SSO 请求和响应结构
class SsoRequest:
    def __init__(self, method, uri, cookie_header):
        self.method = method
        self.uri = uri
        self.cookie_header = cookie_header


class SsoInfo:
    def __init__(self, **kwargs):
        self.redirect_uri = kwargs.get('redirectUri')
        self.redirect_code = kwargs.get('redirectCode')
        self.ct_user_id = kwargs.get('ctUserId')
        self.ct_account_id = kwargs.get('ctAccountId')
        self.is_root = kwargs.get('isRoot')
        self.email = kwargs.get('email')
        self.username = kwargs.get('username')
        self.user_type = kwargs.get('userType')
        self.real_name = kwargs.get('realName')
        self.city = kwargs.get('city')
        self.uid = kwargs.get('uid')
        self.agent_id = kwargs.get('agentId')
        self.event_type = kwargs.get('eventType')
        self.yunnao_flag = kwargs.get('yunnaoFlag')
        self.delegate = kwargs.get('delegate')
        self.delegate_ctyun_acct_id = kwargs.get('delegateCtyunAcctId')
        self.delegate_ctyun_user_id = kwargs.get('delegateCtyunUserId')
        self.delegate_email = kwargs.get('delegateEmail')
        self.delegate_name = kwargs.get('delegateName')
        self.delegate_phone = kwargs.get('delegatePhone')


class SsoResponse:
    def __init__(self, status_code, return_obj, error, message):
        self.status_code = status_code
        self.return_obj = return_obj
        self.error = error
        self.message = message


# 错误和成功处理函数
def default_success_handler(request, sso_info):
    # 将用户信息设置到请求的 header 中
    set_sso_user_header(request, sso_info)


def default_error_handler(error):
    return {"msg": "login has error", "code": 400, "message": "login has error"}


# 设置 SSO 用户信息到 header
def set_sso_user_header(request, user_info):
    request.META['HTTP_SSO_ACCOUNTID'] = user_info.ct_account_id
    request.META['HTTP_SSO_USERID'] = user_info.ct_user_id
    request.META['HTTP_SSO_EMAIL'] = user_info.email
    request.META['HTTP_SSO_ISROOT'] = user_info.is_root
    request.META['HTTP_SSO_NAME'] = user_info.username
    request.META['HTTP_SSO_USERTYPE'] = user_info.user_type
    request.META['HTTP_SSO_REALNAME'] = user_info.real_name
    request.META['HTTP_SSO_CITY'] = user_info.city
    request.META['HTTP_SSO_UID'] = user_info.uid
    request.META['HTTP_SSO_AGENTID'] = user_info.agent_id
    request.META['HTTP_SSO_EVENTTYPE'] = user_info.event_type
    request.META['HTTP_SSO_YUNNAOFLAG'] = user_info.yunnao_flag
    request.META['HTTP_SSO_DELEGATE'] = user_info.delegate
    request.META['HTTP_SSO_DELEGATECTYUNACCTID'] = user_info.delegate_ctyun_acct_id
    request.META['HTTP_SSO_DELEGATECTYUNUSERID'] = user_info.delegate_ctyun_user_id
    request.META['HTTP_SSO_DELEGATEEMAIL'] = user_info.delegate_email
    request.META['HTTP_SSO_DELEGATENAME'] = user_info.delegate_name
    request.META['HTTP_SSO_DELEGATEPHONE'] = user_info.delegate_phone


# 错误处理函数
def handler_not_login(request, sso_response):
    if sso_response.return_obj.redirect_code == 302:
        # 302 的话，直接跳转
        return HttpResponseRedirect(sso_response.return_obj.redirect_uri)
    # sso 返回的错误码只有两种，401 和 302
    return JsonResponse(json.loads(sso_response.return_obj.redirect_uri), status=401)


# 请求 SSO 用户信息
def get_sso_user_info(sso_address, request):
    try:
        sso_auth_url = f"{sso_address}/console/sso/sdk/cas/userInfo"
        sso_auth_req_params = {
            'method': request.method,
            'uri': request.path,
        }
        cookies = request.COOKIES
        headers = {k: v for k, v in cookies.items() if k.lower() in ssoNeedCookieList}
        # 如果接入了链路追踪的话，会传递TRACEID到下游的话，可以不用加
        headers['traceid'] = request.META.get('HTTP_TRACEID','')
        headers['traceparent'] = request.META.get('HTTP_TRACEPARENT','')

        sso_resp = requests.get(sso_auth_url, params=sso_auth_req_params, headers=headers, timeout=5)
        sso_resp.raise_for_status()

        response_data = sso_resp.json()
        sso_response = SsoResponse(
            status_code=response_data.get('statusCode'),
            return_obj=SsoInfo(**response_data.get('returnObj', {})),
            error=response_data.get('error'),
            message=response_data.get('message')
        )
        return sso_response, None
    except requests.RequestException as err:
        logger.error(f"Error in SSO auth request: {err}")
        return None, err


# 定义 Django 中间件
class SsoDjangoAuthMiddleware(MiddlewareMixin):
    def __init__(self, get_response):
        super().__init__(get_response)
        self.sso_address = DEFAULT_SSO_ADDRESS
        self.success_handler = DEFAULT_SUCCESS_HANDLER or default_success_handler
        self.error_handler = DEFAULT_ERROR_HANDLER or default_error_handler

        if not self.sso_address:
            raise ValueError("SSO address is empty, please check the configuration.")

    def process_request(self, request):
        sso_response, error = get_sso_user_info(self.sso_address, request)
        # 调用sso 接口异常比如超时
        if error:
            return JsonResponse(self.error_handler(error), status=400)

        # 校验失败
        if sso_response.status_code != 800:
            return handler_not_login(request, sso_response)

        # 鉴权成功，设置用户信息，可以自定义设置在自己习惯的地方，默认是放在header，并且拼接上SSO_
        # 这种设置方式和目前控制台后端是保持一致的
        self.success_handler(request, sso_response.return_obj)
        return None
```

### CAS 登录、登出、委托签名、代理登录 handler 接入

```go
import (
	"log"
	"net/http"
	"work.ctyun.cn/git/console-common/sso-go-sdk/cas"
)

func main() {
	// 创建CAS配置
	config := &cas.Config{
		Client: &cas.ClientConfig{
			Host:                     "https://www.ctyun.cn",
			Login:                    "/login",
			AppId:                    "your-app-id",
			AppSecret:                "your-app-secret",
			DefaultSessionTtlMinutes: 180, // 3小时
			ConsoleIndex:             "/console/index/",
			Sp:                       "ctId",
		},
		Server: &cas.ServerConfig{
			Host:           "https://console.ctyun.cn",
			Login:          "/cas/login",
			TicketValidate: "/cas/serviceValidate",
			Logout:         "/cas/logout",
		},
	}

	// 创建会话管理器（可选择内存或Redis）
	// 使用内存会话管理器
	sessionManager := cas.NewMemorySessionManager(1024 * 1024) // 1MB缓存

	// 或者使用Redis会话管理器
	// redisClient := redis.NewClient(&redis.Options{
	//     Addr:     "localhost:6379",
	//     Password: "",
	//     DB:       0,
	// })
	// sessionManager, err := cas.NewRedisSessionManager(cas.WithRedisClient(redisClient))
	// if err != nil {
	//     log.Fatal("创建Redis会话管理器失败:", err)
	// }

	// 创建CAS处理器
	handler := cas.NewHandler(config, sessionManager,
		// 在 WithSuccessHandler 和 WithErrorHandler 处理需要注意以下问题:
		// response 因为已经塞入了 302 重定向, 改写 response 可能无法生效并且会报错
		
		// 可选：自定义成功处理器, 若对请求结果无特殊处理可以不加
		cas.WithSuccessHandler(func(w http.ResponseWriter, r *http.Request) {
			log.Println("CAS操作成功")
		}),
		// 可选：自定义错误处理器, 若对请求结果无特殊处理可以不加
		cas.WithErrorHandler(func(w http.ResponseWriter, r *http.Request, err error) {
			// 对于 Login 和 DelegateSign 来说 handler 会给 response 填入 302 重定向到登录页
			log.Printf("CAS错误: %v", err)
		}),
	)

	// 注册路由, 若是使用 gin 或者 go-zero 可以把 handler.Login() 再套一层封装
	// go-gin 可以使用 gin.WrapF(handler.Login())
	
	// 处理 IT ticket&redirectUrl&logoutRequest 请求
	http.HandleFunc("/login", handler.Login())

	// 处理登出请求, 清理 cookie 和缓存
	http.HandleFunc("/logout", handler.Logout())

	// 当从 bcp 代理登录时, 前端需要通过委托签名来换取 ctId 从而获取到被委托人的资源池视图
	http.HandleFunc("/sign", handler.DelegateSign())

	// 委托登录, 处理 agentId&ticket&redirectUrl 请求, 即来自 bcp 代理登录的请求
	http.HandleFunc("/", handler.LoginFromAgent())

	// 启动服务器
	log.Println("服务器启动在 :8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}
```

### CAS 登入后 context 中获取用户信息 middleware 接入

#### 标准 HTTP 中间件

```go
import (
	"fmt"
	"log"
	"net/http"
	"work.ctyun.cn/git/console-common/sso-go-sdk/cas"
)

func main() {
	// 创建会话管理器（与上面handler示例中的配置相同）, 生产环境请使用 redis session manager
	sessionManager := cas.NewMemorySessionManager(1024 * 1024)

	// 创建认证中间件, WithUnauthorizedHandler 为可选项, 默认
	authMiddleware := cas.NewMiddleware(sessionManager).
		WithUnauthorizedHandler(func(w http.ResponseWriter, r *http.Request, err error) bool {
			// 返回false表示停止处理请求，返回true表示继续处理
			if err != nil {
				log.Printf("认证失败: %v", err)
			}
			http.Error(w, "未授权访问", http.StatusUnauthorized)
			return false // 停止处理请求
		})

	// 3. 使用中间件保护路由
	protectedHandler := authMiddleware.Auth(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 从context中获取用户信息
		user := cas.GetLoginUser(r)
		if user != nil {
			fmt.Fprintf(w, "欢迎用户: %s (ID: %s)\n", user.AuthUserID.Name, user.AuthUserID.UserID)
			fmt.Fprintf(w, "邮箱: %s\n", user.AuthUserID.Email)
			fmt.Fprintf(w, "账户ID: %s\n", user.AuthUserID.AccountID)
			fmt.Fprintf(w, "是否主用户: %s\n", user.AuthUserID.IsRoot)
		} else {
			fmt.Fprintf(w, "未找到用户信息")
		}
	}))

	http.Handle("/protected", protectedHandler)

	log.Println("服务器启动在 :8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}
```

#### Gin 框架中间件

```go
import (
	"net/http"
	"github.com/gin-gonic/gin"
	"work.ctyun.cn/git/console-common/sso-go-sdk/middleware/cas"
)

func main() {
	// 1. 创建会话管理器
	sessionManager := cas.NewMemorySessionManager(1024 * 1024)

	// 2. 创建认证中间件
	authMiddleware := cas.NewMiddleware(sessionManager).
		WithUnauthorizedHandler(func(w http.ResponseWriter, r *http.Request, err error) bool {
			// 对于Gin，通常返回false停止处理
			return false
		})

	// 3. 创建Gin路由
	r := gin.Default()

	// 4. 使用CAS认证中间件
	r.Use(authMiddleware.ForGin())

	// 5. 定义受保护的路由
	r.GET("/user-info", func(c *gin.Context) {
		// 从Gin context中获取用户信息
		user := cas.GetLoginUserFromGin(c)
		if user != nil {
			c.JSON(http.StatusOK, gin.H{
				"message":   "获取用户信息成功",
				"userId":    user.AuthUserID.UserID,
				"userName":  user.AuthUserID.Name,
				"email":     user.AuthUserID.Email,
				"accountId": user.AuthUserID.AccountID,
				"isRoot":    user.AuthUserID.IsRoot,
				"userType":  user.AuthUserID.UserType,
				"realName":  user.AuthUserID.RealName,
				"city":      user.AuthUserID.City,
			})
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "未找到用户信息",
			})
		}
	})

	r.Run(":8080")
}
```

#### Go-Zero 框架中间件

```go
import (
	"fmt"
	"log"
	"net/http"
	"work.ctyun.cn/git/console-common/sso-go-sdk/middleware/cas"
)

func main() {
	// 1. 创建会话管理器
	sessionManager := cas.NewMemorySessionManager(1024 * 1024)

	// 2. 创建认证中间件
	authMiddleware := cas.NewMiddleware(sessionManager).
		WithUnauthorizedHandler(func(w http.ResponseWriter, r *http.Request, err error) bool {
			http.Error(w, "未授权访问", http.StatusUnauthorized)
			return false
		})

	// 3. 定义业务处理函数
	userInfoHandler := func(w http.ResponseWriter, r *http.Request) {
		// 从context中获取用户信息
		user := cas.GetLoginUser(r)
		if user != nil {
			fmt.Fprintf(w, `{
				"userId": "%s",
				"userName": "%s",
				"email": "%s",
				"accountId": "%s",
				"isRoot": "%s"
			}`, user.AuthUserID.UserID, user.AuthUserID.Name,
				user.AuthUserID.Email, user.AuthUserID.AccountID, user.AuthUserID.IsRoot)
		} else {
			http.Error(w, "未找到用户信息", http.StatusUnauthorized)
		}
	}

	// 4. 使用中间件包装处理函数
	protectedHandler := authMiddleware.ForGoZero(userInfoHandler)

	http.HandleFunc("/user-info", protectedHandler)

	log.Println("Go-Zero服务器启动在 :8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}
```

### 用户信息结构说明

```go
// LoginUser 包含完整的用户会话信息
type LoginUser struct {
	UUID                string     // 会话唯一标识符
	Ticket              string     // CAS服务票据
	Enabled             bool       // 登录是否启用
	AuthUserID          *AuthUserID // 用户认证详情
	SessionTTL          int64      // 会话生存时间（秒）
	UID                 string     // 用户唯一标识符
	AgentID             string     // 代理标识符
	EventType           string     // 登录事件类型
	YunnaoFlag          string     // 云脑标志
	Delegate            string     // 委托标志
	DelegateCTYunUserID string     // 委托天翼云用户ID
	DelegateCTYunAcctID string     // 委托天翼云账户ID
	DelegateEmail       string     // 委托邮箱
	DelegatePhone       string     // 委托电话
	DelegateName        string     // 委托名称
}

// AuthUserID 用户认证详情
type AuthUserID struct {
	UserID    string // 天翼云用户ID
	AccountID string // 天翼云账号ID
	Email     string // 用户邮箱
	IsRoot    string // 是否主用户 ("1"表示是，"0"表示否)
	Name      string // 用户名
	UserType  string // 用户类型
	RealName  string // 实名
	City      string // 城市
}
```

### 配置说明

#### ClientConfig 客户端配置

```go
type ClientConfig struct {
	Host                     string // 应用主机地址
	Login                    string // 登录路径，默认 "/login"
	AppId                    string // CAS客户端应用ID
	AppSecret                string // CAS客户端应用密钥
	DefaultSessionTtlMinutes int64  // 默认会话时长（分钟），默认180分钟
	ConsoleIndex             string // 控制台首页路径，默认 "/console/index/"
	Sp                       string // 委托签名SP，默认 "ctId"
}
```

#### ServerConfig 服务器配置

```go
type ServerConfig struct {
	Host           string // CAS服务器地址
	Login          string // CAS登录路径，默认 "/cas/login"
	TicketValidate string // 票据验证路径，默认 "/cas/serviceValidate"
	Logout         string // CAS登出路径，默认 "/cas/logout"
}
```

### 会话管理器选择

#### 内存会话管理器（适用于单机部署）

```go
// 创建内存会话管理器，参数为缓存大小（字节）
sessionManager := cas.NewMemorySessionManager(1024 * 1024) // 1MB缓存
```

#### Redis会话管理器（适用于分布式部署）

```go
import "github.com/redis/go-redis/v9"

// 创建Redis客户端
redisClient := redis.NewClient(&redis.Options{
	Addr:     "localhost:6379",
	Password: "",
	DB:       0,
})

// 创建Redis会话管理器
sessionManager, err := cas.NewRedisSessionManager(cas.WithRedisClient(redisClient))
if err != nil {
	log.Fatal("创建Redis会话管理器失败:", err)
}
```

### 使用说明

1. **配置CAS服务器信息**：设置正确的CAS服务器地址和应用凭据
2. **选择会话管理器**：根据部署架构选择内存或Redis会话管理器
3. **注册处理器**：将CAS处理器注册到对应的路由上
4. **使用认证中间件**：在需要保护的路由上使用认证中间件
5. **获取用户信息**：在业务逻辑中通过context获取已认证用户信息

这些示例展示了如何：
- 配置和创建CAS处理器来处理登录、登出、委托签名和代理登录
- 在不同框架中使用认证中间件来保护路由
- 从请求上下文中获取已认证用户的详细信息
- 自定义错误处理逻辑