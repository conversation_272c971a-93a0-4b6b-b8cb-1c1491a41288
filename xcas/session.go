package xcas

import (
	"context"
	"time"
)

const (
	userTicketUUIDPrefix = "sso:user_ticket_uuid:"
	uuidLoginUserPrefix  = ":1:django.contrib.sessions.cache"
)

// SessionManager 定义会话操作的接口
type SessionManager interface {
	// StoreLoginUser 在会话中存储登录用户
	StoreLoginUser(ctx context.Context, user *LoginUser) error

	// GetLoginUser 通过UUID检索登录用户
	GetLoginUser(ctx context.Context, uuid string) (*LoginUser, error)

	// DeleteLoginUser 通过UUID删除登录用户会话
	DeleteLoginUser(ctx context.Context, uuid string) error

	// DeleteLoginUserByTicket 通过票据删除登录用户会话
	DeleteLoginUserByTicket(ctx context.Context, ticket string) error

	// RefreshUserSession 刷新用户会话的过期时间
	RefreshUserSession(ctx context.Context, uuid string, user *LoginUser, ttl time.Duration) error
}
