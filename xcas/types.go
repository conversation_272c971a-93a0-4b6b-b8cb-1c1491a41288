package xcas

import (
	"errors"
)

type Config struct {
	Client *ClientConfig
	Server *ServerConfig
}

// ClientConfig CAS客户端配置
type ClientConfig struct {
	// Host 主机地址
	Host string `json:"host"`

	// Login 登录地址
	Login string `json:"login"`

	// AppId cas client appId
	AppId string `json:"appId"`

	// AppSecret cas client appSecret
	AppSecret string `json:"appSecret"`

	// DefaultSessionTtlMinutes console的session的默认时间期限，单位：分钟
	DefaultSessionTtlMinutes int64 `json:"defaultSessionTtlMinutes"`

	// ConsoleIndex /console/index
	ConsoleIndex string `json:"consoleIndex"`

	// Sp delegateSignSp ctId
	Sp string `json:"sp"`
}

// DefaultClientConfig 默认ClientConfig实例
func DefaultClientConfig() *ClientConfig {
	return &ClientConfig{
		Host:                     "",
		Login:                    "/login",
		AppId:                    "",
		AppSecret:                "",
		DefaultSessionTtlMinutes: 180,
		ConsoleIndex:             "/console/index/",
		Sp:                       "ctId",
	}
}

// buildLoginPath 构建登录路径
func (c *ClientConfig) buildLoginPath() (string, error) {
	if c.Host == "" {
		return "", errors.New("cas client host is empty")
	}
	if c.Login == "" {
		return "", errors.New("cas client path is empty")
	}
	return joinURL(c.Host, c.Login), nil
}

// ServerConfig CAS服务器配置
type ServerConfig struct {
	// Host cas server host
	Host string `json:"host"`

	// Login 登录地址
	Login string `json:"login"`

	// TicketValidate ticket 验证地址
	TicketValidate string `json:"ticketValidate"`

	// Logout 登出地址
	Logout string `json:"logout"`
}

// DefaultServerConfig 创建默认ServerConfig实例
func DefaultServerConfig() *ServerConfig {
	return &ServerConfig{
		Host:           "",
		Login:          "/cas/login",
		TicketValidate: "/cas/validate",
		Logout:         "/cas/logout",
	}
}

// LoginUser 表示来自CAS的已认证用户
type LoginUser struct {
	// UUID 此登录会话的唯一标识符
	UUID string `json:"uuid"`

	// Ticket CAS服务票据
	Ticket string `json:"ticket"`

	// Enabled 此登录是否启用
	Enabled bool `json:"enabled"`

	// AuthUserID 用户标识详情
	AuthUserID *AuthUserID `json:"authUserId"`

	// SessionTTL 会话生存时间（秒）
	SessionTTL int64 `json:"sessionTTL"`

	// UID 用户唯一标识符
	UID string `json:"uid"`

	// AgentID 委托登录的代理标识符
	AgentID string `json:"agentId"`

	// EventType 登录的事件类型
	EventType string `json:"eventType"`

	// YunnaoFlag 云脑标志
	YunnaoFlag string `json:"yunnaoFlag"`

	// Delegate 委托标志
	Delegate string `json:"delegate"`

	// DelegateCTYunUserID 委托天翼云用户ID
	DelegateCTYunUserID string `json:"delegateCtyunUserId"`

	// DelegateCTYunAcctID 委托天翼云账户ID
	DelegateCTYunAcctID string `json:"delegateCtyunAcctId"`

	// DelegateEmail 委托邮箱
	DelegateEmail string `json:"delegateEmail"`

	// DelegatePhone 委托电话
	DelegatePhone string `json:"delegatePhone"`

	// DelegateName 委托名称
	DelegateName string `json:"delegateName"`
}

// AuthUserID 表示用户认证详情
type AuthUserID struct {
	// UserID 天翼云用户ID
	UserID string `json:"userId"`

	// AccountID 天翼云账号id
	AccountID string `json:"accountId"`

	// Email 登录天翼云用户邮箱
	Email string `json:"email"`

	// IsRoot 是否是主用户
	IsRoot string `json:"isRoot"`

	// Name 用户名
	Name string `json:"name"`

	// UserType 用户类型
	UserType string `json:"userType"`

	// RealName 实名
	RealName string `json:"realName"`

	// City 城市
	City string `json:"city"`
}

// OpenapiV4Response 通用API响应结构
type OpenapiV4Response struct {
	// ReturnObj 具体的返回对象
	ReturnObj any `json:"returnObj,omitempty"`

	// StatusCode 状态码，800表示成功，900表示失败
	StatusCode int `json:"statusCode"`

	// Error 错误码
	Error string `json:"error,omitempty"`

	// Message 错误消息
	Message string `json:"message,omitempty"`
}

// DelegateSignResponse 委托签名响应
type DelegateSignResponse struct {
	// CtId 用户ID
	CtId string `json:"ctId"`

	// Now 时间戳
	Now string `json:"now"`

	// App appId
	App string `json:"app"`

	// Sp ctId
	Sp string `json:"sp"`

	// Signature 签名
	Signature string `json:"signature"`
}
