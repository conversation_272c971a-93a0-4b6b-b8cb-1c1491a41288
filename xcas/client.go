package xcas

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"

	"gopkg.in/cas.v2"
)

// clientWrapper 包装cas.Client以提供更多功能
type clientWrapper struct {
	cli *http.Client
}

// newClientWrapper 创建一个新的cas.Client包装器
func newClientWrapper(options *cas.Options) *clientWrapper {
	return &clientWrapper{
		cli: &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
		},
	}
}

// validateUrlForServiceTicket 使用自定义URL验证服务票据
func (c *clientWrapper) validateUrlForServiceTicket(validationURL string) (*cas.AuthenticationResponse, error) {
	// 获取验证响应
	resp, err := c.cli.Get(validationURL)
	if err != nil {
		return nil, fmt.Errorf("获取验证响应失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取验证响应失败: %w", err)
	}

	// 解析CAS验证响应
	authResp, err := cas.ParseServiceResponse(body)
	if err != nil {
		return nil, fmt.Errorf("解析CAS响应失败: %w", err)
	}

	return authResp, nil
}
