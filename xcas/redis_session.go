package xcas

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

type RedisSessionManagerOptionFn func(o *redisSessionManagerOptions)

// WithRedisClient 直接传入redis客户端实例用于 session 管理, 优先级高于 WithRedisOptions, 推荐使用
func WithRedisClient(cli redis.UniversalClient) RedisSessionManagerOptionFn {
	return func(o *redisSessionManagerOptions) {
		o.client = cli
	}
}

func WithRedisPrefix(ticketPrefix, userPrefix string) RedisSessionManagerOptionFn {
	return func(o *redisSessionManagerOptions) {
		o.ticketKeyPrefix = ticketPrefix
		o.userKeyPrefix = userPrefix
	}
}

// WithRedisOptions 传入 redis option 让 middleware 使用临时的 redis 客户端实例
// 不推荐使用, 因为无法明确实例的生命周期, 无法得知业务什么时候需要关闭实例连接
// Deprecated
//func WithRedisOptions(options *redis.UniversalOptions) RedisSessionManagerOptionFn {
//	return func(o *redisSessionManagerOptions) {
//		o.options = options
//	}
//}

// redisSessionManagerOptions Redis会话管理器的配置选项
type redisSessionManagerOptions struct {
	// Redis客户端配置
	options *redis.UniversalOptions

	// Redis 实例
	client redis.UniversalClient

	// Redis键前缀
	ticketKeyPrefix, userKeyPrefix string
}

// redisSessionManager 使用Redis官方客户端实现SessionManager
type redisSessionManager struct {
	// Redis客户端
	client redis.UniversalClient
	// Redis键前缀
	ticketKeyPrefix, userKeyPrefix string
}

// NewRedisSessionManager 创建一个使用Redis官方客户端的会话管理器
func NewRedisSessionManager(opts ...RedisSessionManagerOptionFn) (SessionManager, error) {
	opt := &redisSessionManagerOptions{
		ticketKeyPrefix: userTicketUUIDPrefix,
		userKeyPrefix:   uuidLoginUserPrefix,
	}
	for _, applyFn := range opts {
		applyFn(opt)
	}

	if opt.client == nil && opt.options == nil {
		return nil, errors.New("redis客户端配置为空")
	}

	// 创建Redis客户端
	client := opt.client
	if opt.client == nil {
		client = redis.NewUniversalClient(opt.options)
	}

	// 测试连接
	_, err := client.Ping(context.Background()).Result()
	if err != nil {
		return nil, fmt.Errorf("连接Redis失败: %w", err)
	}

	return &redisSessionManager{
		client:          client,
		ticketKeyPrefix: opt.ticketKeyPrefix,
		userKeyPrefix:   opt.userKeyPrefix,
	}, nil
}

// StoreLoginUser 在Redis中存储登录用户
func (r *redisSessionManager) StoreLoginUser(ctx context.Context, user *LoginUser) (err error) {
	if user == nil || user.UUID == "" {
		return errors.New("无效的登录用户")
	}

	// JSON编码用户
	userData, err := json.Marshal(user)
	if err != nil {
		return fmt.Errorf("序列化用户失败: %w", err)
	}

	// 确定TTL
	ttl := time.Duration(user.SessionTTL) * time.Second

	// 用户键(UUID -> 用户信息)
	userKey := r.userKeyPrefix + user.UUID

	if err = r.client.Set(ctx, userKey, userData, ttl).Err(); err != nil {
		return fmt.Errorf("redis执行失败: %w", err)
	}

	// 票据键(票据 -> UUID)
	if user.Ticket != "" {
		ticketKey := r.ticketKeyPrefix + user.Ticket
		if err = r.client.Set(ctx, ticketKey, user.UUID, ttl).Err(); err != nil {
			r.client.Del(ctx, userKey) // 回滚
			return fmt.Errorf("redis执行失败: %w", err)
		}
	}

	return
}

// GetLoginUser 从Redis通过UUID检索登录用户
func (r *redisSessionManager) GetLoginUser(ctx context.Context, uuid string) (user *LoginUser, err error) {
	if uuid == "" {
		return nil, errors.New("无效的UUID")
	}

	// 从Redis获取用户数据
	userKey := r.userKeyPrefix + uuid
	data, err := r.client.Get(ctx, userKey).Result()

	if errors.Is(err, redis.Nil) {
		return nil, fmt.Errorf("找不到UUID对应的用户: %s", uuid)
	} else if err != nil {
		return nil, fmt.Errorf("从redis获取用户失败: %w", err)
	}

	// 反序列化用户数据
	if err = json.Unmarshal([]byte(data), &user); err != nil {
		return nil, fmt.Errorf("反序列化用户失败: %w", err)
	}

	return
}

// DeleteLoginUser 从Redis通过UUID删除登录用户会话
func (r *redisSessionManager) DeleteLoginUser(ctx context.Context, uuid string) (err error) {
	if uuid == "" {
		return errors.New("无效的UUID")
	}

	// 先获取用户以找到票据
	user, err := r.GetLoginUser(ctx, uuid)
	if err != nil {
		// 用户未找到，无需删除
		return nil
	}

	// 删除用户键
	userKey := r.userKeyPrefix + uuid
	if user.Ticket == "" {
		if err = r.client.Del(ctx, userKey).Err(); err != nil {
			return fmt.Errorf("redis删除失败: %w", err)
		}
	} else {
		ticketKey := r.ticketKeyPrefix + user.Ticket
		if err = r.client.Del(ctx, userKey, ticketKey).Err(); err != nil {
			return fmt.Errorf("redis删除失败: %w", err)
		}
	}

	return
}

// DeleteLoginUserByTicket 通过票据删除登录用户会话
func (r *redisSessionManager) DeleteLoginUserByTicket(ctx context.Context, ticket string) (err error) {
	if ticket == "" {
		return errors.New("无效的票据")
	}

	// 获取票据对应的UUID
	ticketKey := r.ticketKeyPrefix + ticket
	uuid, err := r.client.Get(ctx, ticketKey).Result()
	if err != nil {
		// 票据不存在，无需操作
		if errors.Is(err, redis.Nil) {
			return nil
		}
		return fmt.Errorf("从redis获取票据失败: %w", err)
	}
	userKey := r.userKeyPrefix + uuid

	if err = r.client.Del(ctx, ticketKey, userKey).Err(); err != nil {
		return fmt.Errorf("redis删除操作失败: %w", err)
	}

	return
}

// RefreshUserSession 刷新用户会话的过期时间
func (r *redisSessionManager) RefreshUserSession(ctx context.Context, uuid string, user *LoginUser, ttl time.Duration) (err error) {
	if uuid == "" {
		return errors.New("无效的UUID")
	}

	// 刷新用户键
	userKey := r.userKeyPrefix + uuid

	// 刷新用户键的过期时间
	if err = r.client.Expire(ctx, userKey, ttl).Err(); err != nil {
		return fmt.Errorf("刷新会话过期时间失败: %w", err)
	}

	// 如果有票据，也刷新票据键的过期时间
	if user.Ticket != "" {
		ticketKey := r.ticketKeyPrefix + user.Ticket
		if err = r.client.Expire(ctx, ticketKey, ttl).Err(); err != nil {
			return fmt.Errorf("刷新会话过期时间失败: %w", err)
		}
	}

	return
}

// Close 关闭Redis连接
func (r *redisSessionManager) Close() error {
	return r.client.Close()
}
