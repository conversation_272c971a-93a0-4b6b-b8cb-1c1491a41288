package xcas

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"strconv"
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
)

// TestDelegateHandlerHandle_AuthenticatedUser 测试已登录用户的委托签名生成
func TestDelegateHandlerHandle_AuthenticatedUser(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	config.Client.Sp = "test-sp" // 设置委托签名Sp
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := DelegateSignHandler(config, sessionManager)

	// 创建一个已登录的测试用户
	ctx := context.Background()
	uuid := "test-delegate-sign-uuid"
	user := &LoginUser{
		UUID:    uuid,
		Enabled: true,
		AuthUserID: &AuthUserID{
			UserID:    "test-user-id",
			AccountID: "test-account-id",
			Email:     "<EMAIL>",
			Name:      "Test User",
		},
		SessionTTL: 3600,
	}

	// 存储用户
	err := sessionManager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建一个带有会话Cookie的请求
	r := httptest.NewRequest("GET", "http://client.example.com/delegate-sign", nil)
	r = r.WithContext(ctx)
	cookie := &http.Cookie{
		Name:  "consolesessionid",
		Value: uuid,
	}
	r.AddCookie(cookie)

	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该返回200 OK
	if rsp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, rsp.StatusCode)
	}

	// 检查Content-Type
	contentType := rsp.Header.Get("Content-Type")
	if contentType != "application/json" {
		t.Errorf("Expected Content-Type %q, got %q", "application/json", contentType)
	}

	// 解析响应
	apiResponse, err := parseAPIResponse(rsp.Body)
	if err != nil {
		t.Fatalf("Failed to parse API response: %v", err)
	}

	// 验证状态码
	if apiResponse.StatusCode != OpenapiSuccessCode {
		t.Errorf("Expected status code %d, got %d, error: %s, message: %s",
			OpenapiSuccessCode, apiResponse.StatusCode, apiResponse.Error, apiResponse.Message)
	}

	// 解析委托签名响应
	delegateSignResponse, err := parseDelegateSignResponse(apiResponse)
	if err != nil {
		t.Fatalf("Failed to parse delegate sign response: %v", err)
	}
	if delegateSignResponse == nil {
		t.Fatal("Expected delegate sign response to be non-nil")
	}

	// 验证委托签名响应字段
	if delegateSignResponse.App != config.Client.AppId {
		t.Errorf("Expected App to be %q, got %q", config.Client.AppId, delegateSignResponse.App)
	}
	if delegateSignResponse.CtId != user.AuthUserID.UserID {
		t.Errorf("Expected CtId to be %q, got %q", user.AuthUserID.UserID, delegateSignResponse.CtId)
	}
	if delegateSignResponse.Sp != config.Client.Sp {
		t.Errorf("Expected Sp to be %q, got %q", config.Client.Sp, delegateSignResponse.Sp)
	}
	if delegateSignResponse.Signature == "" {
		t.Error("Expected Signature to be non-empty")
	}

	// 验证签名格式（base64编码的HMAC-SHA256）
	// 典型的base64编码的HMAC-SHA256签名长度为43个字符（去掉填充）
	if len(delegateSignResponse.Signature) < 40 {
		t.Errorf("Signature appears too short: %q", delegateSignResponse.Signature)
	}

	// 验证时间戳
	_, err = strconv.ParseInt(delegateSignResponse.Now, 10, 64)
	if err != nil {
		t.Errorf("Expected Now to be a valid timestamp, got %q: %v", delegateSignResponse.Now, err)
	}
}

// TestDelegateHandlerHandle_UnauthenticatedUser 测试未登录用户的处理
func TestDelegateHandlerHandle_UnauthenticatedUser(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := DelegateSignHandler(config, sessionManager)

	// 创建一个没有会话Cookie的请求（未登录）
	r := httptest.NewRequest("GET", "http://client.example.com/delegate-sign", nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 未登录用户应该被重定向到登录页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 验证重定向URL指向登录页面
	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Server.Host+config.Server.Login) {
		t.Errorf("Expected location to start with %q, got %q", config.Server.Host+config.Server.Login, location)
	}
}

// TestDelegateHandlerHandle_MissingUserID 测试已登录但缺少UserID的情况
func TestDelegateHandlerHandle_MissingUserID(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := DelegateSignHandler(config, sessionManager)

	// 创建一个已登录但缺少UserID的测试用户
	ctx := context.Background()
	uuid := "test-missing-userid-uuid"
	user := &LoginUser{
		UUID:    uuid,
		Enabled: true,
		AuthUserID: &AuthUserID{
			// 没有设置UserID
			AccountID: "test-account-id",
			Email:     "<EMAIL>",
			Name:      "Test User",
		},
		SessionTTL: 3600,
	}

	// 存储用户
	err := sessionManager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建一个带有会话Cookie的请求
	r := httptest.NewRequest("GET", "http://client.example.com/delegate-sign", nil)
	r = r.WithContext(ctx)
	cookie := &http.Cookie{
		Name:  "consolesessionid",
		Value: uuid,
	}
	r.AddCookie(cookie)

	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 缺少UserID的用户应该被重定向到登录页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 验证重定向URL指向登录页面
	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Server.Host+config.Server.Login) {
		t.Errorf("Expected location to start with %q, got %q", config.Server.Host+config.Server.Login, location)
	}
}

// TestDelegateHandlerHandle_WithTimestamp 测试提供timestamp参数的情况
func TestDelegateHandlerHandle_WithTimestamp(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	config.Client.Sp = "test-sp" // 设置委托签名Sp
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := DelegateSignHandler(config, sessionManager)

	// 创建一个已登录的测试用户
	ctx := context.Background()
	uuid := "test-timestamp-uuid"
	user := &LoginUser{
		UUID:    uuid,
		Enabled: true,
		AuthUserID: &AuthUserID{
			UserID:    "test-user-id",
			AccountID: "test-account-id",
			Email:     "<EMAIL>",
			Name:      "Test User",
		},
		SessionTTL: 3600,
	}

	// 存储用户
	err := sessionManager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建一个自定义时间戳
	customTimestamp := "*************" // 2021-10-07T00:00:00.000Z

	// 创建一个带有会话Cookie和timestamp参数的请求
	r := httptest.NewRequest("GET", "http://client.example.com/delegate-sign?timestamp="+customTimestamp, nil)
	r = r.WithContext(ctx)
	cookie := &http.Cookie{
		Name:  "consolesessionid",
		Value: uuid,
	}
	r.AddCookie(cookie)

	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该返回200 OK
	if rsp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, rsp.StatusCode)
	}

	// 解析响应
	apiResponse, err := parseAPIResponse(rsp.Body)
	if err != nil {
		t.Fatalf("Failed to parse API response: %v", err)
	}

	// 验证状态码
	if apiResponse.StatusCode != OpenapiSuccessCode {
		t.Errorf("Expected status code %d, got %d, error: %s, message: %s",
			OpenapiSuccessCode, apiResponse.StatusCode, apiResponse.Error, apiResponse.Message)
	}

	// 解析委托签名响应
	delegateSignResponse, err := parseDelegateSignResponse(apiResponse)
	if err != nil {
		t.Fatalf("Failed to parse delegate sign response: %v", err)
	}
	if delegateSignResponse == nil {
		t.Fatal("Expected delegate sign response to be non-nil")
	}

	// 验证委托签名响应中的时间戳是我们提供的
	if delegateSignResponse.Now != customTimestamp {
		t.Errorf("Expected Now to be %q, got %q", customTimestamp, delegateSignResponse.Now)
	}
}

// TestCalculateDelegateSignature 测试签名计算函数
func TestCalculateDelegateSignature(t *testing.T) {
	// 创建配置
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建委托签名处理器
	d := &delegateSignHandler{
		service: &service{
			clientConfig:   config.Client,
			serverConfig:   config.Server,
			sessionManager: sessionManager,
		},
	}

	// 定义基本的测试数据
	userId := "test-user-id"
	timestamp := "*************" // 2021-10-07T00:00:00.000Z

	// 测试用例
	tests := []struct {
		name        string
		userId      string
		timestamp   string
		appId       string
		appSecret   string
		wantErr     bool
		errContains string
	}{
		{
			name:      "Standard Case",
			userId:    userId,
			timestamp: timestamp,
			appId:     config.Client.AppId,
			appSecret: config.Client.AppSecret,
			wantErr:   false,
		},
		{
			name:      "Different UserId",
			userId:    "different-user-id",
			timestamp: timestamp,
			appId:     config.Client.AppId,
			appSecret: config.Client.AppSecret,
			wantErr:   false,
		},
		{
			name:      "Different Timestamp",
			userId:    userId,
			timestamp: "1633532500000", // 一分钟后
			appId:     config.Client.AppId,
			appSecret: config.Client.AppSecret,
			wantErr:   false,
		},
		{
			name:        "Invalid AppSecret",
			userId:      userId,
			timestamp:   timestamp,
			appId:       config.Client.AppId,
			appSecret:   "invalid-base64-~~~",
			wantErr:     true,
			errContains: "illegal base64 data",
		},
		{
			name:      "Empty UserId",
			userId:    "",
			timestamp: timestamp,
			appId:     config.Client.AppId,
			appSecret: config.Client.AppSecret,
			wantErr:   false, // 通常应该是错误，但根据实现可能允许空userId
		},
		{
			name:      "Empty Timestamp",
			userId:    userId,
			timestamp: "",
			appId:     config.Client.AppId,
			appSecret: config.Client.AppSecret,
			wantErr:   false, // 通常应该是错误，但根据实现可能允许空timestamp
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试用例中的appId和appSecret
			d.service.clientConfig.AppId = tt.appId
			d.service.clientConfig.AppSecret = tt.appSecret

			// 计算签名
			signature, err := d.calculateDelegateSignature(tt.userId, tt.timestamp)

			// 验证错误
			if (err != nil) != tt.wantErr {
				t.Errorf("calculateDelegateSignature() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果期望错误并且有错误字符串，验证错误消息
			if tt.wantErr && tt.errContains != "" && err != nil {
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("calculateDelegateSignature() error = %v, want to contain %v", err, tt.errContains)
				}
				return
			}

			// 如果不期望错误，验证签名不为空
			if !tt.wantErr {
				if signature == "" {
					t.Error("calculateDelegateSignature() returned empty signature")
				}

				// 典型的base64编码的HMAC-SHA256签名长度为43个字符（去掉填充）
				if len(signature) < 40 {
					t.Errorf("Signature appears too short: %q", signature)
				}

				// 对于同样的输入应该得到相同的输出，验证签名的一致性
				signature2, err := d.calculateDelegateSignature(tt.userId, tt.timestamp)
				if err != nil {
					t.Errorf("calculateDelegateSignature() second call error = %v", err)
				}
				if signature != signature2 {
					t.Errorf("calculateDelegateSignature() not consistent: first = %v, second = %v", signature, signature2)
				}
			}
		})
	}
}

// TestLoginFromAgentHandler_Success 测试成功的委托登录请求
func TestLoginFromAgentHandler_Success(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LoginFromAgentHandler(config, sessionManager)

	// 使用gomonkey模拟http.Client.do方法，用于模拟CAS服务器的响应
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个成功的响应
			return createTestSuccessResponse()
		})
	defer patch.Reset()

	// 设置重定向URL
	redirectURL := "http://redirect.example.com/dashboard"
	encodedRedirect := url.QueryEscape(redirectURL)

	// 创建请求，包含委托登录所需的参数
	r := httptest.NewRequest("GET", "http://client.example.com/login-from-agent?agentId=test-agent&ticket=ST-1234-valid&redirect="+encodedRedirect, nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 对于有效票据的委托登录，应该重定向到redirect参数指定的URL
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 应该重定向到提供的redirect URL
	location := rsp.Header.Get("Location")
	if location != redirectURL {
		t.Errorf("Expected location to be %q, got %q", redirectURL, location)
	}

	// 应该设置会话Cookie
	var sessionCookie *http.Cookie
	for _, cookie := range rsp.Cookies() {
		if cookie.Name == "consolesessionid" {
			sessionCookie = cookie
			break
		}
	}

	if sessionCookie == nil {
		t.Error("Expected consolesessionid cookie to be set")
	} else {
		// 验证会话用户是否被存储
		user, err := sessionManager.GetLoginUser(context.Background(), sessionCookie.Value)
		if err != nil {
			t.Errorf("Failed to get user: %v", err)
		}
		if user == nil {
			t.Error("Expected user to be stored in session")
		} else {
			// 验证用户信息是否正确
			if user.AuthUserID.UserID != "test-user-id" {
				t.Errorf("Expected user ID to be 'test-user-id', got %q", user.AuthUserID.UserID)
			}

			// 验证代理ID是否设置
			if user.AgentID != "test-agent" {
				t.Errorf("Expected AgentID to be 'test-agent', got %q", user.AgentID)
			}
		}
	}
}

// TestLoginFromAgentHandler_WithResourceId 测试带resourceId参数的委托登录
func TestLoginFromAgentHandler_WithResourceId(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LoginFromAgentHandler(config, sessionManager)

	// 使用gomonkey模拟http.Client.do方法，用于模拟CAS服务器的响应
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个成功的响应
			return createTestSuccessResponse()
		})
	defer patch.Reset()

	// 设置重定向URL和resourceId
	redirectURL := "http://redirect.example.com/dashboard"
	encodedRedirect := url.QueryEscape(redirectURL)
	resourceId := "resource-123"

	// 创建请求，包含委托登录所需的参数以及resourceId
	r := httptest.NewRequest("GET", "http://client.example.com/login-from-agent?agentId=test-agent&ticket=ST-1234-valid&redirect="+encodedRedirect+"&resourceId="+resourceId, nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 对于有效票据的委托登录，应该重定向到redirect参数指定的URL，并附加resourceId
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 应该重定向到提供的redirect URL并附加resourceId参数
	expectedLocation := redirectURL + "?resourceId=" + resourceId
	location := rsp.Header.Get("Location")
	if location != expectedLocation {
		t.Errorf("Expected location to be %q, got %q", expectedLocation, location)
	}

	// 验证会话Cookie
	var sessionCookie *http.Cookie
	for _, cookie := range rsp.Cookies() {
		if cookie.Name == "consolesessionid" {
			sessionCookie = cookie
			break
		}
	}

	if sessionCookie == nil {
		t.Error("Expected consolesessionid cookie to be set")
	}
}

// TestLoginFromAgentHandler_NoRedirect 测试没有redirect参数的委托登录
func TestLoginFromAgentHandler_NoRedirect(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LoginFromAgentHandler(config, sessionManager)

	// 使用gomonkey模拟http.Client.do方法，用于模拟CAS服务器的响应
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个成功的响应
			return createTestSuccessResponse()
		})
	defer patch.Reset()

	// 创建请求，只包含委托登录的基本参数，没有redirect参数
	r := httptest.NewRequest("GET", "http://client.example.com/login-from-agent?agentId=test-agent&ticket=ST-1234-valid", nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该返回错误，因为redirect参数是必需的
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 获取响应的Location头
	location := rsp.Header.Get("Location")
	if location == "" {
		t.Error("Expected Location header to be set")
	}

	// 对于没有redirect参数的情况，loginFromAgent应该回退到默认重定向
	// 注意：这取决于具体实现，可能重定向到登录页面或首页 FIXME: 目前 java 中的逻辑默认是一定有 redirect 参数的
	//if !strings.HasPrefix(location, config.Server.Host+config.Server.Login) {
	//	t.Errorf("Expected Location to start with %q for missing redirect, got %q",
	//		config.Server.Host+config.Server.Login, location)
	//}
}

// TestLoginFromAgentHandler_InvalidTicket 测试无效票据的委托登录
func TestLoginFromAgentHandler_InvalidTicket(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LoginFromAgentHandler(config, sessionManager)

	// 使用gomonkey模拟http.Client.do方法，返回票据验证失败的响应
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个失败的响应
			return createTestFailureResponse("INVALID_TICKET", "Ticket ST-1234-invalid not recognized")
		})
	defer patch.Reset()

	// 设置重定向URL
	redirectURL := "http://redirect.example.com/dashboard"
	encodedRedirect := url.QueryEscape(redirectURL)

	// 创建请求，包含委托登录所需的参数，但使用无效票据
	r := httptest.NewRequest("GET", "http://client.example.com/login-from-agent?agentId=test-agent&ticket=ST-1234-invalid&redirect="+encodedRedirect, nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 对于无效票据，应该重定向到登录页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 应该重定向到CAS服务器的登录页面
	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Server.Host+config.Server.Login) {
		t.Errorf("Expected Location to start with %q, got %q", config.Server.Host+config.Server.Login, location)
	}

	// 验证没有设置会话Cookie
	var foundValidSessionCookie bool
	for _, cookie := range rsp.Cookies() {
		if cookie.Name == "consolesessionid" && cookie.Value != "" && cookie.MaxAge > 0 {
			foundValidSessionCookie = true
			break
		}
	}

	if foundValidSessionCookie {
		t.Error("Unexpected valid session cookie found for invalid ticket")
	}
}

// TestLoginFromAgentHandler_WithExtraParams 测试带额外参数的委托登录
func TestLoginFromAgentHandler_WithExtraParams(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LoginFromAgentHandler(config, sessionManager)

	// 使用gomonkey模拟http.Client.do方法，用于模拟CAS服务器的响应
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个成功的响应
			return createTestSuccessResponse()
		})
	defer patch.Reset()

	// 设置重定向URL和额外参数
	redirectURL := "http://redirect.example.com/dashboard"
	encodedRedirect := url.QueryEscape(redirectURL)
	eventTypeValue := "test-event"
	yunnaoFlagValue := "true"

	// 创建请求，包含委托登录所需的参数以及额外参数
	r := httptest.NewRequest("GET", "http://client.example.com/login-from-agent?agentId=test-agent&ticket=ST-1234-valid&redirect="+
		encodedRedirect+"&"+queryKeyEventType+"="+eventTypeValue+"&"+queryKeyYunnaoFlag+"="+yunnaoFlagValue, nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 对于有效票据的委托登录，应该重定向到redirect参数指定的URL
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 应该重定向到提供的redirect URL
	location := rsp.Header.Get("Location")
	if location != redirectURL {
		t.Errorf("Expected location to be %q, got %q", redirectURL, location)
	}

	// 应该设置会话Cookie
	var sessionCookie *http.Cookie
	for _, cookie := range rsp.Cookies() {
		if cookie.Name == "consolesessionid" {
			sessionCookie = cookie
			break
		}
	}

	if sessionCookie == nil {
		t.Error("Expected consolesessionid cookie to be set")
		return
	}

	// 验证会话用户是否被存储
	user, err := sessionManager.GetLoginUser(context.Background(), sessionCookie.Value)
	if err != nil {
		t.Errorf("Failed to get user: %v", err)
		return
	}
	if user == nil {
		t.Error("Expected user to be stored in session")
		return
	}

	// 验证用户信息是否正确
	if user.AuthUserID.UserID != "test-user-id" {
		t.Errorf("Expected user ID to be 'test-user-id', got %q", user.AuthUserID.UserID)
	}

	// 验证额外参数是否被正确设置
	if user.AgentID != "test-agent" {
		t.Errorf("Expected AgentID to be 'test-agent', got %q", user.AgentID)
	}
	if user.EventType != eventTypeValue {
		t.Errorf("Expected EventType to be %q, got %q", eventTypeValue, user.EventType)
	}
	if user.YunnaoFlag != yunnaoFlagValue {
		t.Errorf("Expected YunnaoFlag to be %q, got %q", yunnaoFlagValue, user.YunnaoFlag)
	}
}

// TestLoginFromAgentHandler_NonDelegateLogin 测试非委托登录流程（没有agentId和ticket）
func TestLoginFromAgentHandler_NonDelegateLogin(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LoginFromAgentHandler(config, sessionManager)

	// 测试两种情况：已登录用户和未登录用户

	// 情况1：已登录用户 - 应该重定向到首页
	t.Run("LoggedInUser", func(t *testing.T) {
		// 创建一个已登录的测试用户
		ctx := context.Background()
		uuid := "test-non-delegate-logged-in-uuid"
		user := &LoginUser{
			UUID:       uuid,
			Enabled:    true,
			SessionTTL: 3600,
			AuthUserID: &AuthUserID{
				UserID:    "test-user-id",
				AccountID: "test-account-id",
				Email:     "<EMAIL>",
				Name:      "Test User",
			},
		}

		// 存储用户
		err := sessionManager.StoreLoginUser(ctx, user)
		if err != nil {
			t.Fatalf("Failed to store user: %v", err)
		}

		// 创建一个带有会话Cookie的请求，但没有委托登录参数
		r := httptest.NewRequest("GET", "http://client.example.com/login-from-agent", nil)
		r = r.WithContext(ctx)
		cookie := &http.Cookie{
			Name:  "consolesessionid",
			Value: uuid,
		}
		r.AddCookie(cookie)

		w := httptest.NewRecorder()

		// 处理请求
		handler(w, r)

		// 验证响应
		rsp := w.Result()
		defer rsp.Body.Close()

		// 对于已登录用户，应该重定向到首页
		if rsp.StatusCode != http.StatusFound {
			t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
		}

		// 应该重定向到控制台首页
		location := rsp.Header.Get("Location")
		if !strings.HasPrefix(location, config.Client.ConsoleIndex) {
			t.Errorf("Expected Location to start with %q, got %q", config.Client.ConsoleIndex, location)
		}
	})

	// 情况2：未登录用户 - 应该重定向到登录页面
	t.Run("NotLoggedInUser", func(t *testing.T) {
		// 创建一个没有会话Cookie的请求，且没有委托登录参数
		r := httptest.NewRequest("GET", "http://client.example.com/login-from-agent", nil)
		w := httptest.NewRecorder()

		// 处理请求
		handler(w, r)

		// 验证响应
		rsp := w.Result()
		defer rsp.Body.Close()

		// 对于未登录用户，应该重定向到登录页面
		if rsp.StatusCode != http.StatusFound {
			t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
		}

		// 应该重定向到CAS服务器的登录页面
		location := rsp.Header.Get("Location")
		if !strings.HasPrefix(location, config.Server.Host+config.Server.Login) {
			t.Errorf("Expected Location to start with %q, got %q", config.Server.Host+config.Server.Login, location)
		}
	})
}

// 解析API响应
func parseAPIResponse(body io.ReadCloser) (*OpenapiV4Response, error) {
	var response OpenapiV4Response
	err := json.NewDecoder(body).Decode(&response)
	return &response, err
}

// 解析委托签名响应
func parseDelegateSignResponse(response *OpenapiV4Response) (*DelegateSignResponse, error) {
	if response.ReturnObj == nil {
		return nil, nil
	}

	// 将interface{}转换为map[string]interface{}
	returnObjMap, ok := response.ReturnObj.(map[string]any)
	if !ok {
		return nil, nil
	}

	// 重新编码为JSON
	jsonData, err := json.Marshal(returnObjMap)
	if err != nil {
		return nil, err
	}

	// 解码为DelegateSignResponse
	var delegateSignResponse DelegateSignResponse
	err = json.Unmarshal(jsonData, &delegateSignResponse)
	if err != nil {
		return nil, err
	}

	return &delegateSignResponse, nil
}
