package xcas

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
)

// TestAuthMiddleware 测试通用HTTP认证中间件
func TestAuthMiddleware(t *testing.T) {
	// 创建会话管理器
	sessionManager := NewMemorySessionManager(1024 * 1024)
	ctx := context.Background()

	// 创建测试用户
	user := &LoginUser{
		UUID:       "test-auth-uuid",
		Ticket:     "ST-auth-test",
		Enabled:    true,
		SessionTTL: 3600,
		AuthUserID: &AuthUserID{
			UserID: "test-user-id",
			Name:   "Test User",
		},
	}

	// 存储用户
	if err := sessionManager.StoreLoginUser(ctx, user); err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建认证中间件
	authMiddleware := NewMiddleware(sessionManager)

	// 创建一个测试处理器，用于验证用户信息是否被添加到上下文中
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 从请求上下文中获取用户信息
		userFromContext := GetLoginUser(r)
		if userFromContext == nil {
			t.Error("Expected user to be added to context, but got nil")
		} else if userFromContext.UUID != user.UUID {
			t.Errorf("Expected user UUID to be %q, got %q", user.UUID, userFromContext.UUID)
		}
		w.WriteHeader(http.StatusOK)
	})

	// 包装测试处理器
	handler := authMiddleware.Handler(testHandler)

	// 创建带有会话Cookie的请求
	r := httptest.NewRequest("GET", "/protected", nil)
	r.AddCookie(&http.Cookie{
		Name:  "consolesessionid",
		Value: user.UUID,
	})
	w := httptest.NewRecorder()

	// 处理请求
	handler.ServeHTTP(w, r)

	// 验证响应
	resp := w.Result()
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}
}

// TestAuthMiddleware_NoSession 测试没有会话的情况
func TestAuthMiddleware_NoSession(t *testing.T) {
	// 创建会话管理器
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建认证中间件
	authMiddleware := NewMiddleware(sessionManager)

	// 创建一个测试处理器，用于验证用户信息是否被添加到上下文中
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 从请求上下文中获取用户信息
		userFromContext := GetLoginUser(r)
		if userFromContext != nil {
			t.Error("Expected user to be nil for unauthenticated request")
		}
		w.WriteHeader(http.StatusOK)
	})

	// 包装测试处理器
	handler := authMiddleware.Handler(testHandler)

	// 创建没有会话Cookie的请求
	r := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler.ServeHTTP(w, r)

	// 验证响应
	resp := w.Result()
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}
}

// TestGinAuthMiddleware 测试Gin认证中间件
func TestGinAuthMiddleware(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建会话管理器
	sessionManager := NewMemorySessionManager(1024 * 1024)
	ctx := context.Background()

	// 创建测试用户
	user := &LoginUser{
		UUID:       "test-gin-uuid",
		Ticket:     "ST-gin-test",
		Enabled:    true,
		SessionTTL: 3600,
		AuthUserID: &AuthUserID{
			UserID: "test-user-id",
			Name:   "Test User",
		},
	}

	// 存储用户
	if err := sessionManager.StoreLoginUser(ctx, user); err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建认证中间件
	authMiddleware := NewMiddleware(sessionManager)

	// 创建Gin路由器
	router := gin.New()

	// 应用中间件
	router.Use(authMiddleware.ForGin())

	// 添加测试路由
	router.GET("/protected", func(c *gin.Context) {
		// 从Gin上下文中获取用户信息
		userFromGin := GetLoginUserFromGin(c)
		if userFromGin == nil {
			t.Error("Expected user to be added to Gin context, but got nil")
		} else if userFromGin.UUID != user.UUID {
			t.Errorf("Expected user UUID to be %q, got %q", user.UUID, userFromGin.UUID)
		}
		c.Status(http.StatusOK)
	})

	// 创建带有会话Cookie的请求
	req := httptest.NewRequest("GET", "/protected", nil)
	req.AddCookie(&http.Cookie{
		Name:  "consolesessionid",
		Value: user.UUID,
	})
	w := httptest.NewRecorder()

	// 处理请求
	router.ServeHTTP(w, req)

	// 验证响应
	resp := w.Result()
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}
}

// TestGinAuthMiddleware_NoSession 测试Gin中间件没有会话的情况
func TestGinAuthMiddleware_NoSession(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建会话管理器
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建认证中间件
	authMiddleware := NewMiddleware(sessionManager)

	// 创建Gin路由器
	router := gin.New()

	// 应用中间件
	router.Use(authMiddleware.ForGin())

	// 添加测试路由
	router.GET("/protected", func(c *gin.Context) {
		// 从Gin上下文中获取用户信息
		userFromGin := GetLoginUserFromGin(c)
		if userFromGin != nil {
			t.Error("Expected user to be nil for unauthenticated request")
		}
		c.Status(http.StatusOK)
	})

	// 创建没有会话Cookie的请求
	req := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	// 处理请求
	router.ServeHTTP(w, req)

	// 验证响应
	resp := w.Result()
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}
}

// TestGoZeroAuthMiddleware 测试Go-Zero认证中间件
func TestGoZeroAuthMiddleware(t *testing.T) {
	// 创建会话管理器
	sessionManager := NewMemorySessionManager(1024 * 1024)
	ctx := context.Background()

	// 创建测试用户
	user := &LoginUser{
		UUID:       "test-gozero-uuid",
		Ticket:     "ST-gozero-test",
		Enabled:    true,
		SessionTTL: 3600,
		AuthUserID: &AuthUserID{
			UserID: "test-user-id",
			Name:   "Test User",
		},
	}

	// 存储用户
	if err := sessionManager.StoreLoginUser(ctx, user); err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建认证中间件
	authMiddleware := NewMiddleware(sessionManager)

	// 创建一个测试处理器，用于验证用户信息是否被添加到上下文中
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 从请求上下文中获取用户信息
		userFromContext := GetLoginUser(r)
		if userFromContext == nil {
			t.Error("Expected user to be added to context, but got nil")
		} else if userFromContext.UUID != user.UUID {
			t.Errorf("Expected user UUID to be %q, got %q", user.UUID, userFromContext.UUID)
		}
		w.WriteHeader(http.StatusOK)
	})

	// 包装测试处理器
	handler := authMiddleware.ForGoZero(testHandler)

	// 创建带有会话Cookie的请求
	r := httptest.NewRequest("GET", "/protected", nil)
	r.AddCookie(&http.Cookie{
		Name:  "consolesessionid",
		Value: user.UUID,
	})
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	resp := w.Result()
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}
}

// TestGoZeroAuthMiddleware_NoSession 测试Go-Zero中间件没有会话的情况
func TestGoZeroAuthMiddleware_NoSession(t *testing.T) {
	// 创建会话管理器
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建认证中间件
	authMiddleware := NewMiddleware(sessionManager)

	// 创建一个测试处理器，用于验证用户信息是否被添加到上下文中
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 从请求上下文中获取用户信息
		userFromContext := GetLoginUser(r)
		if userFromContext != nil {
			t.Error("Expected user to be nil for unauthenticated request")
		}
		w.WriteHeader(http.StatusOK)
	})

	// 包装测试处理器
	handler := authMiddleware.ForGoZero(testHandler)

	// 创建没有会话Cookie的请求
	r := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	resp := w.Result()
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}
}

// TestAuthMiddleware_WithErrorHandler 测试标准HTTP中间件中的错误处理
func TestAuthMiddleware_WithErrorHandler(t *testing.T) {
	// 创建会话管理器
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 测试场景1：自定义处理函数返回true，请求继续处理
	t.Run("ContinueProcessing", func(t *testing.T) {
		// 创建一个标记变量，用于验证处理器是否被调用
		handlerCalled := false
		testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			handlerCalled = true
			w.WriteHeader(http.StatusOK)
		})

		// 创建自定义错误处理函数
		errorHandlerCalled := false
		errorHandler := func(w http.ResponseWriter, r *http.Request, err error) bool {
			errorHandlerCalled = true
			// 注意：当没有会话时，这里的 err 可能不是 nil
			return true // 继续处理请求
		}

		// 创建认证中间件
		authMiddleware := NewMiddleware(sessionManager).WithUnauthorizedHandler(errorHandler)

		// 包装测试处理器
		handler := authMiddleware.Handler(testHandler)

		// 创建没有会话Cookie的请求
		r := httptest.NewRequest("GET", "/protected", nil)
		w := httptest.NewRecorder()

		// 处理请求
		handler.ServeHTTP(w, r)

		// 验证错误处理函数被调用
		if !errorHandlerCalled {
			t.Error("Expected error handler to be called")
		}

		// 验证处理器被调用（因为错误处理函数返回true）
		if !handlerCalled {
			t.Error("Expected handler to be called when error handler returns true")
		}

		// 验证响应状态码
		resp := w.Result()
		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}
	})

	// 测试场景2：自定义处理函数返回false，请求不继续处理
	t.Run("StopProcessing", func(t *testing.T) {
		// 创建一个标记变量，用于验证处理器是否被调用
		handlerCalled := false
		testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			handlerCalled = true
			w.WriteHeader(http.StatusOK)
		})

		// 创建自定义错误处理函数
		errorHandler := func(w http.ResponseWriter, r *http.Request, err error) bool {
			w.WriteHeader(http.StatusForbidden)
			return false // 不继续处理请求
		}

		// 创建认证中间件
		authMiddleware := NewMiddleware(sessionManager).WithUnauthorizedHandler(errorHandler)

		// 包装测试处理器
		handler := authMiddleware.Handler(testHandler)

		// 创建没有会话Cookie的请求
		r := httptest.NewRequest("GET", "/protected", nil)
		w := httptest.NewRecorder()

		// 处理请求
		handler.ServeHTTP(w, r)

		// 验证处理器没有被调用（因为错误处理函数返回false）
		if handlerCalled {
			t.Error("Expected handler not to be called when error handler returns false")
		}

		// 验证响应状态码
		resp := w.Result()
		if resp.StatusCode != http.StatusForbidden {
			t.Errorf("Expected status code %d, got %d", http.StatusForbidden, resp.StatusCode)
		}
	})

	// 测试场景3：会话存在但无效，错误信息正确传递
	t.Run("InvalidSession", func(t *testing.T) {
		ctx := context.Background()

		// 创建一个无效的用户（Enabled = false）
		user := &LoginUser{
			UUID:    "test-invalid-uuid",
			Ticket:  "ST-invalid-test",
			Enabled: false,
		}

		// 存储用户
		if err := sessionManager.StoreLoginUser(ctx, user); err != nil {
			t.Fatalf("Failed to store user: %v", err)
		}

		// 创建一个标记变量，用于验证处理器是否被调用
		handlerCalled := false
		testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			handlerCalled = true
			w.WriteHeader(http.StatusOK)
		})

		// 创建自定义错误处理函数
		errorHandlerCalled := false
		receivedError := error(nil)

		// 创建认证中间件
		errorHandler := func(w http.ResponseWriter, r *http.Request, err error) bool {
			errorHandlerCalled = true
			receivedError = err
			return false // 不继续处理请求
		}
		authMiddleware := NewMiddleware(sessionManager).WithUnauthorizedHandler(errorHandler)

		// 包装测试处理器
		handler := authMiddleware.Handler(testHandler)

		// 创建带有会话Cookie的请求
		r := httptest.NewRequest("GET", "/protected", nil)
		r.AddCookie(&http.Cookie{
			Name:  "consolesessionid",
			Value: user.UUID,
		})
		w := httptest.NewRecorder()

		// 处理请求
		handler.ServeHTTP(w, r)

		// 验证错误处理函数被调用
		if !errorHandlerCalled {
			t.Error("Expected error handler to be called")
		}

		// 验证接收到的错误是否为nil（因为是用户无效而不是错误）
		if receivedError != nil {
			t.Errorf("Expected nil error for disabled user, got %v", receivedError)
		}

		// 验证处理器没有被调用（因为错误处理函数返回false）
		if handlerCalled {
			t.Error("Expected handler not to be called when error handler returns false")
		}
	})
}
