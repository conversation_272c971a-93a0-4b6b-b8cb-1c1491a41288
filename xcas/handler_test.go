package xcas

import (
	"bytes"
	"context"
	"encoding/json"
	"encoding/xml"
	"io"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
)

// TestLoginSuccessHandler 测试登录成功处理器
func TestLoginSuccessHandler(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理器记录器
	recorder := newHandlerRecorder(t)

	// 创建处理器
	handler := NewHandler(config, sessionManager,
		WithSuccessHandler(recorder.successHandler),
		With<PERSON><PERSON>r<PERSON><PERSON><PERSON>(recorder.errorHandler))

	// 获取登录处理函数
	loginHandler := handler.Login()

	// 使用gomonkey模拟http.Client.do方法，用于模拟CAS服务器的响应
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个成功的响应
			return createTestSuccessResponse()
		})
	defer patch.Reset()

	// 创建请求，包含有效票据
	r := httptest.NewRequest("GET", "http://client.example.com/login?ticket=ST-1234-valid", nil)
	w := httptest.NewRecorder()

	// 处理请求
	loginHandler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 对于有效票据，应该重定向到控制台首页
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 应该重定向到控制台首页
	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Client.ConsoleIndex) {
		t.Errorf("Expected location to start with %q, got %q", config.Client.ConsoleIndex, location)
	}

	// 验证成功处理器被调用
	if !recorder.successCalled {
		t.Error("Success handler was not called")
	}

	// 验证错误处理器未被调用
	if recorder.errorCalled {
		t.Error("Error handler was unexpectedly called")
	}
}

// TestLoginErrorHandler 测试登录错误处理器
func TestLoginErrorHandler(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理器记录器
	recorder := newHandlerRecorder(t)

	// 创建处理器
	handler := NewHandler(config, sessionManager,
		WithSuccessHandler(recorder.successHandler),
		WithErrorHandler(recorder.errorHandler))

	// 获取登录处理函数
	loginHandler := handler.Login()

	// 使用gomonkey模拟http.Client.do方法
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个失败的响应
			return createTestFailureResponse("INVALID_TICKET", "Ticket ST-1234-invalid not recognized")
		})
	defer patch.Reset()

	// 创建请求，包含无效票据
	r := httptest.NewRequest("GET", "http://client.example.com/login?ticket=ST-1234-invalid", nil)
	w := httptest.NewRecorder()

	// 处理请求
	loginHandler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到登录页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 验证错误处理器被调用
	if !recorder.errorCalled {
		t.Error("Error handler was not called")
	}

	// 验证成功处理器未被调用
	if recorder.successCalled {
		t.Error("Success handler was unexpectedly called")
	}

	// 验证错误信息
	if recorder.lastError == nil {
		t.Error("Expected error to be passed to error handler")
	}
}

// TestLogoutSuccessHandler 测试登出成功处理器
func TestLogoutSuccessHandler(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 存储一个测试用户
	ctx := context.Background()
	uuid := "test-logout-uuid"
	user := &LoginUser{
		UUID:       uuid,
		Ticket:     "ST-1234-logout-test",
		Enabled:    true,
		SessionTTL: 3600,
	}
	if err := sessionManager.StoreLoginUser(ctx, user); err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建处理器记录器
	recorder := newHandlerRecorder(t)

	// 创建处理器
	handler := NewHandler(config, sessionManager,
		WithSuccessHandler(recorder.successHandler),
		WithErrorHandler(recorder.errorHandler))

	// 获取登出处理函数
	logoutHandler := handler.Logout()

	// 创建请求，包含会话Cookie
	r := httptest.NewRequest("GET", "http://client.example.com/logout", nil)
	cookie := &http.Cookie{
		Name:  "consolesessionid",
		Value: uuid,
	}
	r.AddCookie(cookie)
	w := httptest.NewRecorder()

	// 处理请求
	logoutHandler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到CAS服务器的登出页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Server.Host+config.Server.Logout) {
		t.Errorf("Expected Location to start with %q, got %q", config.Server.Host+config.Server.Logout, location)
	}

	// 验证成功处理器被调用
	if !recorder.successCalled {
		t.Error("Success handler was not called")
	}

	// 验证错误处理器未被调用
	if recorder.errorCalled {
		t.Error("Error handler was unexpectedly called")
	}

	// 验证会话被删除
	_, err := sessionManager.GetLoginUser(ctx, uuid)
	if err == nil {
		t.Error("Expected session to be deleted after logout")
	}
}

// TestDelegateSignSuccessHandler 测试委托签名成功处理器
func TestDelegateSignSuccessHandler(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理器记录器
	recorder := newHandlerRecorder(t)

	// 创建处理器
	handler := NewHandler(config, sessionManager,
		WithSuccessHandler(recorder.successHandler),
		WithErrorHandler(recorder.errorHandler))

	// 创建一个已登录的测试用户
	ctx := context.Background()
	uuid := "test-delegate-sign-uuid"
	user := &LoginUser{
		UUID:    uuid,
		Enabled: true,
		AuthUserID: &AuthUserID{
			UserID:    "test-user-id",
			AccountID: "test-account-id",
			Email:     "<EMAIL>",
			Name:      "Test User",
		},
		SessionTTL: 3600,
	}

	// 存储用户会话
	err := sessionManager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 获取委托签名处理函数
	delegateHandler := handler.DelegateSign()

	// 创建请求并添加会话Cookie
	r := httptest.NewRequest("GET", "http://client.example.com/delegate?returnUrl=http://app.example.com/callback", nil)
	r.AddCookie(&http.Cookie{
		Name:  "consolesessionid",
		Value: uuid,
	})
	w := httptest.NewRecorder()

	// 处理请求
	delegateHandler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该成功响应
	if rsp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, rsp.StatusCode)
	}

	// 验证成功处理器被调用
	if !recorder.successCalled {
		t.Error("Success handler was not called")
	}

	// 验证错误处理器未被调用
	if recorder.errorCalled {
		t.Error("Error handler was unexpectedly called")
	}

	// 验证响应内容是委托签名响应
	body, err := io.ReadAll(rsp.Body)
	if err != nil {
		t.Fatalf("Failed to read response body: %v", err)
	}

	var apiResponse OpenapiV4Response
	err = json.Unmarshal(body, &apiResponse)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// 验证状态码
	if apiResponse.StatusCode != OpenapiSuccessCode {
		t.Errorf("Expected status code %d, got %d, error: %s, message: %s",
			OpenapiSuccessCode, apiResponse.StatusCode, apiResponse.Error, apiResponse.Message)
	}

	// 解析委托签名响应
	delegateSignResponse, err := parseDelegateSignResponse(&apiResponse)
	if err != nil {
		t.Fatalf("Failed to parse delegate sign response: %v", err)
	}
	if delegateSignResponse == nil {
		t.Fatal("Expected delegate sign response to be non-nil")
	}

	// 验证委托签名响应字段
	if delegateSignResponse.CtId != user.AuthUserID.UserID {
		t.Errorf("Expected CtId to be %q, got %q", user.AuthUserID.UserID, delegateSignResponse.CtId)
	}
	if delegateSignResponse.App != config.Client.AppId {
		t.Errorf("Expected App to be %q, got %q", config.Client.AppId, delegateSignResponse.App)
	}
	if delegateSignResponse.Sp != config.Client.Sp {
		t.Errorf("Expected Sp to be %q, got %q", config.Client.Sp, delegateSignResponse.Sp)
	}
	if delegateSignResponse.Signature == "" {
		t.Error("Expected Signature to be non-empty")
	}
}

// TestLoginFromAgentSuccessHandler 测试代理登录成功处理器
func TestLoginFromAgentSuccessHandler(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理器记录器
	recorder := newHandlerRecorder(t)

	// 创建处理器
	handler := NewHandler(config, sessionManager,
		WithSuccessHandler(recorder.successHandler),
		WithErrorHandler(recorder.errorHandler))

	// 获取代理登录处理函数
	agentHandler := handler.LoginFromAgent()

	// 使用gomonkey模拟http.Client.do方法，用于模拟CAS服务器的响应
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个成功的响应
			return createTestSuccessResponse()
		})
	defer patch.Reset()

	// 创建请求，包含代理ID和票据
	r := httptest.NewRequest("GET", "http://client.example.com/loginFromAgent?agentId=test-agent&ticket=ST-1234-agent-test&redirect=http://app.example.com/dashboard", nil)
	w := httptest.NewRecorder()

	// 处理请求
	agentHandler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到指定页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, "http://app.example.com/dashboard") {
		t.Errorf("Expected Location to be redirect URL, got %q", location)
	}

	// 验证成功处理器被调用
	if !recorder.successCalled {
		t.Error("Success handler was not called")
	}

	// 验证错误处理器未被调用
	if recorder.errorCalled {
		t.Error("Error handler was unexpectedly called")
	}
}

// 记录处理器调用情况的结构体
type handlerRecorder struct {
	t             *testing.T
	successCalled bool
	errorCalled   bool
	lastRequest   *http.Request
	lastResponse  http.ResponseWriter
	lastError     error
	responseBody  string
}

func newHandlerRecorder(t *testing.T) *handlerRecorder {
	return &handlerRecorder{t: t}
}

func (h *handlerRecorder) successHandler(w http.ResponseWriter, r *http.Request) {
	h.successCalled = true
	h.lastRequest = r
	h.lastResponse = w
}

func (h *handlerRecorder) errorHandler(w http.ResponseWriter, r *http.Request, err error) {
	h.errorCalled = true
	h.lastRequest = r
	h.lastResponse = w
	h.lastError = err
	// 只记录错误，不让测试失败
	h.t.Logf("%s", err)
}

// 创建一个标准的测试配置
func createTestConfig() *Config {
	return &Config{
		Client: &ClientConfig{
			Host:                     "http://client.example.com",
			Login:                    "/login",
			AppId:                    "test-app-id",
			AppSecret:                "dGVzdC1hcHAtc2VjcmV0", // base64编码的"test-app-secret"
			ConsoleIndex:             "/console/index",
			DefaultSessionTtlMinutes: 180, // 默认180分钟
		},
		Server: &ServerConfig{
			Host:           "http://auth.example.com",
			Login:          "/login",
			Logout:         "/logout",
			TicketValidate: "/cas/validate",
		},
	}
}

// 创建测试成功响应
func createTestSuccessResponse() (*http.Response, error) {
	// 创建CAS成功验证响应
	casResponse := &struct {
		XMLName     xml.Name `xml:"cas:serviceResponse"`
		Xmlns       string   `xml:"xmlns:cas,attr"`
		Success     bool     `xml:"-"`
		AuthSuccess struct {
			User       string `xml:"cas:user"`
			Attributes struct {
				UserID         string `xml:"cas:userId"`
				AccountID      string `xml:"cas:accountId"`
				DomainID       string `xml:"cas:domainId"`
				Email          string `xml:"cas:email"`
				Name           string `xml:"cas:name"`
				Mobile         string `xml:"cas:mobile"`
				State          string `xml:"cas:state"`
				SessionTTLMins string `xml:"cas:sessionTtlMinutes"`
				UID            string `xml:"cas:uid"`
				UserType       string `xml:"cas:userType"`
				RealName       string `xml:"cas:realName"`
				City           string `xml:"cas:city"`
			} `xml:"cas:attributes"`
		} `xml:"cas:authenticationSuccess"`
	}{
		Xmlns:   "http://www.yale.edu/tp/cas",
		Success: true,
	}

	casResponse.AuthSuccess.User = "test-user"
	casResponse.AuthSuccess.Attributes.UserID = "test-user-id"
	casResponse.AuthSuccess.Attributes.AccountID = "test-account-id"
	casResponse.AuthSuccess.Attributes.DomainID = "test-domain-id" // 这是测试期望的AccountID值
	casResponse.AuthSuccess.Attributes.Email = "<EMAIL>"
	casResponse.AuthSuccess.Attributes.Name = "Test User"
	casResponse.AuthSuccess.Attributes.Mobile = "***********"
	casResponse.AuthSuccess.Attributes.State = "active"      // 修正为小写，匹配attrActiveState
	casResponse.AuthSuccess.Attributes.SessionTTLMins = "30" // 30分钟会话
	casResponse.AuthSuccess.Attributes.UID = "test-uid"
	casResponse.AuthSuccess.Attributes.UserType = "normal"
	casResponse.AuthSuccess.Attributes.RealName = "Real Test User"
	casResponse.AuthSuccess.Attributes.City = "Test City"

	// 序列化为XML
	var xmlBuf bytes.Buffer
	xmlBuf.WriteString(`<?xml version="1.0" encoding="UTF-8"?>`)
	encoder := xml.NewEncoder(&xmlBuf)
	encoder.Indent("", "  ")
	if err := encoder.Encode(casResponse); err != nil {
		return nil, err
	}

	// 创建HTTP响应
	return &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(xmlBuf.String())),
	}, nil
}

// 创建测试失败响应
func createTestFailureResponse(code, message string) (*http.Response, error) {
	// 创建CAS验证失败响应
	casResponse := &struct {
		XMLName  xml.Name `xml:"cas:serviceResponse"`
		Xmlns    string   `xml:"xmlns:cas,attr"`
		Success  bool     `xml:"-"`
		AuthFail struct {
			Code    string `xml:"code,attr"`
			Message string `xml:",chardata"`
		} `xml:"cas:authenticationFailure"`
	}{
		Xmlns:   "http://www.yale.edu/tp/cas",
		Success: false,
	}

	casResponse.AuthFail.Code = code
	casResponse.AuthFail.Message = message

	// 序列化为XML
	var xmlBuf bytes.Buffer
	xmlBuf.WriteString(`<?xml version="1.0" encoding="UTF-8"?>`)
	encoder := xml.NewEncoder(&xmlBuf)
	encoder.Indent("", "  ")
	if err := encoder.Encode(casResponse); err != nil {
		return nil, err
	}

	// 创建HTTP响应
	return &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(xmlBuf.String())),
	}, nil
}
