package xcas

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/samber/mo"
	"github.com/spf13/cast"
	"gopkg.in/cas.v2"
)

// service 实现与Java版SsoService类似的功能
type service struct {
	clientConfig   *ClientConfig
	serverConfig   *ServerConfig
	sessionManager SessionManager
	casClient      *clientWrapper
	successHandler http.HandlerFunc
	errorHandler   func(http.ResponseWriter, *http.Request, error)
}

func (s *service) createLoginUser(ctx context.Context, ticket string,
	assertion *cas.AuthenticationResponse, extraMap map[string]any) (*LoginUser, error) {
	// 从断言中提取属性
	attrs := make(map[string]any, len(assertion.Attributes))
	for k, v := range assertion.Attributes {
		if len(v) > 0 {
			attrs[k] = v[0]
		}
	}

	// 检查用户是否处于活动状态
	if !s.isUserActive(attrs) {
		return nil, fmt.Errorf("用户不处于活动状态")
	}

	// 确定会话TTL
	sessionTTL := mo.Some(attrs[attrSessionTTLMinutes]).
		Map(func(value any) (any, bool) { v, e := cast.ToInt64E(value); return v, e == nil }).
		OrElse(s.clientConfig.DefaultSessionTtlMinutes).(int64) * 60 // 默认值（秒）

	// 创建登录用户
	loginUser := newLoginUser(attrs, sessionTTL, extraMap)
	loginUser.UUID = uuid.New().String()
	loginUser.Ticket = ticket
	loginUser.Enabled = true

	// 将用户存储到会话中
	if err := s.sessionManager.StoreLoginUser(ctx, loginUser); err != nil {
		return nil, fmt.Errorf("存储登录用户失败: %w", err)
	}

	return loginUser, nil
}

func (s *service) isUserActive(attrs map[string]any) bool {
	if attrs == nil {
		return false
	}

	state, exists := attrs[attrState]
	if !exists {
		// 没有状态属性，假定为活动状态
		return true
	}

	if stateStr, ok := state.(string); ok && strings.EqualFold(stateStr, attrActiveState) {
		return true
	}

	return false
}

// redirectToLogin 重定向到登录页面
func (s *service) redirectToLogin(w http.ResponseWriter) (err error) {
	// 获取登录URI
	loginUri, err := s.redirectToLoginUri()
	if err != nil {
		return
	}

	// 执行重定向
	w.Header().Set("Location", loginUri)
	w.WriteHeader(http.StatusFound)
	return
}

// redirectToLoginUri 构建CAS服务器登录页面的URI
func (s *service) redirectToLoginUri() (loginUrl string, err error) {
	// 构建客户端登录路径
	clientLoginPath, err := s.clientConfig.buildLoginPath()
	if err != nil {
		return
	}

	// URL编码客户端登录路径
	encodedService := escapeFragment(clientLoginPath)

	// 构建服务器登录URL
	serverLoginPath := joinURL(s.serverConfig.Host, s.serverConfig.Login)

	// 添加service参数
	loginUrl = fmt.Sprintf("%s?service=%s", serverLoginPath, encodedService)

	return
}

// redirectToIndex 重定向到首页
func (s *service) redirectToIndex(w http.ResponseWriter, redirectUrl string) {
	// 确定重定向位置
	location := s.clientConfig.ConsoleIndex

	if redirectUrl != "" {
		location = redirectUrl
	}

	// 执行重定向
	w.Header().Set("Location", location)
	w.WriteHeader(http.StatusFound)
}

// addConsoleCookie 添加控制台Cookie
func (s *service) addConsoleCookie(w http.ResponseWriter, user *LoginUser) {
	if user.SessionTTL > 0 {
		// 设置带有用户UUID的Cookie
		http.SetCookie(w, &http.Cookie{
			Name:     "consolesessionid",
			Value:    user.UUID,
			Path:     "/",
			HttpOnly: true,
			Secure:   true,
		})

		// 用户userId，目前主要想着用来降级
		if user.AuthUserID != nil && user.AuthUserID.UserID != "" {
			http.SetCookie(w, &http.Cookie{
				Name:     "console_user_id",
				Value:    user.AuthUserID.UserID,
				Path:     "/",
				HttpOnly: true,
				Secure:   true,
			})
		}
	} else {
		// 登出 / 失效，设置过期时间
		http.SetCookie(w, &http.Cookie{
			Name:     "consolesessionid",
			Value:    user.UUID,
			Path:     "/",
			HttpOnly: true,
			Secure:   true,
			MaxAge:   int(user.SessionTTL),
			Expires:  time.Now().Add(time.Duration(user.SessionTTL) * time.Second),
		})
	}
}

// addCsrfTokenCookie 添加CSRF令牌Cookie
func (s *service) addCsrfTokenCookie(r *http.Request, w http.ResponseWriter) {
	// 检查CSRF令牌是否已存在
	_, err := r.Cookie("csrftoken")
	if err == nil {
		// 令牌已存在
		return
	}

	// 创建新的CSRF令牌
	token := uuid.New().String()

	// 设置一年过期的Cookie
	http.SetCookie(w, &http.Cookie{
		Name:   "csrftoken",
		Value:  token,
		Path:   "/",
		MaxAge: 365 * 24 * 60 * 60, // 一年
	})
}

// disableConsoleCookie 禁用控制台Cookie
func (s *service) disableConsoleCookie(w http.ResponseWriter) {
	s.addConsoleCookie(w, &LoginUser{UUID: "", SessionTTL: 0})
}

// validateSessionId 验证UUID并刷新会话
func (s *service) validateSessionId(ctx context.Context, uuid string) (*LoginUser, error) {
	if uuid == "" {
		return nil, fmt.Errorf("UUID为空")
	}

	// 获取登录用户
	loginUser, err := s.sessionManager.GetLoginUser(ctx, uuid)
	if err != nil {
		return nil, fmt.Errorf("获取登录用户失败: %w", err)
	}

	// 检查用户是否有效
	if loginUser != nil && loginUser.Enabled {
		// 刷新会话
		_ = s.sessionManager.RefreshUserSession(ctx, uuid, loginUser, time.Duration(loginUser.SessionTTL)*time.Second)
		return loginUser, nil
	}

	return nil, fmt.Errorf("无效的登录用户或用户已禁用")
}

func (s *service) handleSuccess(w http.ResponseWriter, r *http.Request) {
	if s.successHandler != nil {
		s.successHandler(w, r)
	}
}

func (s *service) handleError(w http.ResponseWriter, r *http.Request, err error) {
	if s.errorHandler != nil {
		s.errorHandler(w, r, err)
	}
}

func (s *service) handleSuccessOrError(w http.ResponseWriter, r *http.Request, err error) {
	if err != nil {
		s.handleError(w, r, err)
	} else {
		s.handleSuccess(w, r)
	}
}

// newLoginUser 创建一个新的LoginUser对象
func newLoginUser(attrs map[string]any, sessionTTL int64, extraMap map[string]any) *LoginUser {
	// 创建基本登录用户
	loginUser := &LoginUser{
		AuthUserID: &AuthUserID{
			UserID:    cast.ToString(attrs[attrUserID]),
			AccountID: cast.ToString(attrs[attrDomainID]),
			Email:     cast.ToString(attrs[attrEmail]),
			Name:      cast.ToString(attrs[attrName]),
			UserType:  cast.ToString(attrs[attrUserType]),
			RealName:  cast.ToString(attrs[attrRealName]),
			City:      cast.ToString(attrs[attrCity]),
		},
		SessionTTL:          sessionTTL,
		UID:                 cast.ToString(attrs[attrUid]),
		Delegate:            cast.ToString(attrs[attrDelegate]),
		DelegateCTYunUserID: cast.ToString(attrs[attrDelegateCTYunUserID]),
		DelegateCTYunAcctID: cast.ToString(attrs[attrDelegateCTYunAcctID]),
		DelegateEmail:       cast.ToString(attrs[attrDelegateEmail]),
		DelegateName:        cast.ToString(attrs[attrDelegateName]),
		DelegatePhone:       cast.ToString(attrs[attrDelegatePhone]),
	}

	// 处理特殊的IsRoot字段
	ctyunRootUserId := cast.ToString(attrs[attrCTYunRootUserID])
	if loginUser.AuthUserID != nil {
		if loginUser.AuthUserID.UserID == ctyunRootUserId && ctyunRootUserId != "" {
			loginUser.AuthUserID.IsRoot = "1"
		} else {
			loginUser.AuthUserID.IsRoot = "0"
		}
	}

	// 处理额外字段
	if len(extraMap) >= 0 {
		loginUser.AgentID = cast.ToString(extraMap[queryKeyAgentID])
		loginUser.EventType = cast.ToString(extraMap[queryKeyEventType])

		// 尝试从两个可能的键获取yunnaoFlag
		yunnaoFlag := cast.ToString(extraMap[queryKeyYunnaoFlag])
		if yunnaoFlag == "" {
			yunnaoFlag = cast.ToString(extraMap[queryKeyYunnaoFlagV2])
		}
		loginUser.YunnaoFlag = yunnaoFlag
	}

	return loginUser
}
