package xcas

import (
	"context"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"
)

// TestLogoutHandlerActiveLogout 测试主动登出流程
func TestLogoutHandlerActiveLogout(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LogoutHandler(config, sessionManager)

	// 创建一个测试用户
	ctx := context.Background()
	uuid := "test-active-logout-uuid"
	user := &LoginUser{
		UUID:    uuid,
		Enabled: true,
		AuthUserID: &AuthUserID{
			UserID: "test-user-id",
		},
		SessionTTL: 3600,
	}

	// 存储用户
	err := sessionManager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建一个带有会话Cookie的请求
	r := httptest.NewRequest("GET", "http://client.example.com/logout", nil)
	r = r.WithContext(ctx)
	cookie := &http.Cookie{
		Name:  "consolesessionid",
		Value: uuid,
	}
	r.AddCookie(cookie)

	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到登出URL
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 验证重定向URL
	expectedLocation := config.Server.Host + config.Server.Logout + "?service=" + url.QueryEscape(config.Client.Host+config.Client.ConsoleIndex)
	if location := rsp.Header.Get("Location"); location != expectedLocation {
		t.Errorf("Expected Location header %q, got %q", expectedLocation, location)
	}

	// 打印所有cookie的详细信息
	t.Logf("Found %d cookies", len(rsp.Cookies()))
	for i, cookie := range rsp.Cookies() {
		t.Logf("Cookie %d: Name=%s, Value=%s, MaxAge=%d, Path=%s, Domain=%s, Secure=%v, HttpOnly=%v",
			i, cookie.Name, cookie.Value, cookie.MaxAge, cookie.Path, cookie.Domain, cookie.Secure, cookie.HttpOnly)
	}

	// 检查会话Cookie是否被禁用
	var foundDisabledCookie bool
	for _, item := range rsp.Cookies() {
		if item.Name == "consolesessionid" && item.MaxAge == 0 {
			foundDisabledCookie = true
			break
		}
	}

	if !foundDisabledCookie {
		t.Error("Expected consolesessionid cookie to be disabled (MaxAge=0)")
	}

	// 验证会话是否已被删除
	_, err = sessionManager.GetLoginUser(ctx, uuid)
	if err == nil {
		t.Error("Expected error when retrieving deleted session")
	}
}

// TestLogoutHandlerWithCookie 测试带有Cookie的登出请求
func TestLogoutHandlerWithCookie(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LogoutHandler(config, sessionManager)

	// 创建一个测试用户
	ctx := context.Background()
	uuid := "test-cookie-logout-uuid"
	user := &LoginUser{
		UUID:    uuid,
		Enabled: true,
		AuthUserID: &AuthUserID{
			UserID: "test-user-id",
		},
		SessionTTL: 3600,
	}

	// 存储用户
	err := sessionManager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建一个带有会话Cookie的请求
	r := httptest.NewRequest("GET", "http://client.example.com/logout", nil)
	r = r.WithContext(ctx)
	cookie := &http.Cookie{
		Name:  "consolesessionid",
		Value: uuid,
	}
	r.AddCookie(cookie)

	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到登出URL
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 验证重定向URL
	expectedLocation := config.Server.Host + config.Server.Logout + "?service=" + url.QueryEscape(config.Client.Host+config.Client.ConsoleIndex)
	if location := rsp.Header.Get("Location"); location != expectedLocation {
		t.Errorf("Expected Location header %q, got %q", expectedLocation, location)
	}

	// 验证会话是否已被删除
	_, err = sessionManager.GetLoginUser(ctx, uuid)
	if err == nil {
		t.Error("Expected error when retrieving deleted session")
	}
}

// TestLogoutHandlerWithoutCookie 测试没有Cookie的登出请求
func TestLogoutHandlerWithoutCookie(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LogoutHandler(config, sessionManager)

	// 创建一个不带会话Cookie的请求
	r := httptest.NewRequest("GET", "http://client.example.com/logout", nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到登出URL
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 验证重定向URL
	expectedLocation := config.Server.Host + config.Server.Logout + "?service=" + url.QueryEscape(config.Client.Host+config.Client.ConsoleIndex)
	if location := rsp.Header.Get("Location"); location != expectedLocation {
		t.Errorf("Expected Location header %q, got %q", expectedLocation, location)
	}
}
