package xcas

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"net/http"
	"strconv"
	"strings"
	"time"
)

type delegateSignHandler struct {
	*service
}

// DelegateSignHandler 创建委托签名中间件
func DelegateSignHandler(config *Config, sessionManager SessionManager) http.HandlerFunc {
	hdr := &delegateSignHandler{
		service: &service{
			clientConfig:   config.Client,
			serverConfig:   config.Server,
			sessionManager: sessionManager,
		},
	}
	return hdr.Handle
}

// Handle 处理委托签名请求
func (d *delegateSignHandler) Handle(w http.ResponseWriter, r *http.Request) {
	// 获取timestamp参数，如果没有则使用当前时间
	timestamp := r.URL.Query().Get("timestamp")
	if timestamp == "" {
		timestamp = strconv.FormatInt(time.Now().UnixMilli(), 10)
	}

	// 从请求中解析UUID
	uuid := parseConsoleSessionUUID(r)

	// 验证用户是否登录
	loginUser, err := d.validateSessionId(r.Context(), uuid)
	if err != nil || loginUser == nil {
		// 未登录，重定向到登录页面
		err = errors.Join(err, d.redirectToLogin(w))
		d.handleSuccessOrError(w, r, err)
		return
	}

	if loginUser.AuthUserID == nil || loginUser.AuthUserID.UserID == "" {
		err = errors.Join(err, d.redirectToLogin(w))
		d.handleSuccessOrError(w, r, err)
		return
	}

	// 准备响应
	rsp := new(OpenapiV4Response)

	// 计算签名
	signature, err := d.calculateDelegateSignature(loginUser.AuthUserID.UserID, timestamp)
	if err != nil {
		// 签名计算失败
		rsp.StatusCode = OpenapiFailCode
		rsp.Error = DelegateSignErrorCode
		rsp.Message = DelegateSignErrorMsg + ": " + err.Error()
	} else {
		// 设置成功响应
		rsp.StatusCode = OpenapiSuccessCode
		rsp.ReturnObj = DelegateSignResponse{
			App:       d.clientConfig.AppId,
			Now:       timestamp,
			Sp:        d.clientConfig.Sp,
			CtId:      loginUser.AuthUserID.UserID,
			Signature: signature,
		}
	}

	// 写入HTTP响应
	w.Header().Set("Content-Type", "application/json")
	_ = json.NewEncoder(w).Encode(rsp)
	d.handleSuccessOrError(w, r, err)
}

// calculateDelegateSignature 计算委托签名
func (d *delegateSignHandler) calculateDelegateSignature(userId, timestamp string) (string, error) {
	// 构建要签名的字符串: appId:timestamp:userId
	toSign := d.clientConfig.AppId + ":" + timestamp + ":" + userId

	// 对接门户单点时用的appSecret
	key, err := base64.RawURLEncoding.DecodeString(strings.TrimRight(d.clientConfig.AppSecret, "="))
	if err != nil {
		return "", err
	}

	// 创建HMAC-SHA256实例
	h := hmac.New(sha256.New, key)
	if _, err = h.Write([]byte(toSign)); err != nil {
		return "", err
	}

	// 使用URL安全的base64编码方式对签名进行编码
	signature := base64.RawURLEncoding.EncodeToString(h.Sum(nil))
	return signature, nil
}
