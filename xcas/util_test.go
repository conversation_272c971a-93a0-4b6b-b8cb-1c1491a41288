package xcas

import (
	"testing"
)

func TestGetTextForElement_Simple(t *testing.T) {
	// 简单XML测试
	xmlStr := `<root><element>value</element></root>`
	value, err := getTextForElement(xmlStr, "element")
	if err != nil {
		t.Fatalf("getTextForElement failed: %v", err)
	}
	if value != "value" {
		t.<PERSON><PERSON>("Expected 'value', got '%s'", value)
	}
}

func TestGetTextForElement_WithNamespace(t *testing.T) {
	// 带命名空间的XML测试
	xmlStr := `<ns:root xmlns:ns="http://example.com"><ns:element>value</ns:element></ns:root>`
	value, err := getTextForElement(xmlStr, "element")
	if err != nil {
		t.Fatalf("getTextForElement failed: %v", err)
	}
	if value != "value" {
		t.<PERSON>("Expected 'value', got '%s'", value)
	}
}

func TestGetTextForElement_NotFound(t *testing.T) {
	// 元素不存在测试
	xmlStr := `<root><element>value</element></root>`
	_, err := getTextForElement(xmlStr, "nonexistent")
	if err == nil {
		t.Errorf("Expected error for non-existent element")
	}
}

func TestGetTextForElement_Empty(t *testing.T) {
	// 空输入测试
	_, err := getTextForElement("", "element")
	if err == nil {
		t.Errorf("Expected error for empty XML string")
	}

	_, err = getTextForElement("<root></root>", "")
	if err == nil {
		t.Errorf("Expected error for empty element name")
	}
}

func TestGetTextForElement_NestedElements(t *testing.T) {
	// 嵌套元素测试
	xmlStr := `<root><parent><element>value</element></parent></root>`
	value, err := getTextForElement(xmlStr, "element")
	if err != nil {
		t.Fatalf("getTextForElement failed: %v", err)
	}
	if value != "value" {
		t.Errorf("Expected 'value', got '%s'", value)
	}
}

func TestParseSAMLLogoutRequest(t *testing.T) {
	// 测试SAML登出请求解析
	logoutRequest := `<samlp:LogoutRequest ID="123" Version="2.0">
		<samlp:SessionIndex>ST-123456-abcdef</samlp:SessionIndex>
	</samlp:LogoutRequest>`

	sessionIndex, err := parseSAMLLogoutRequest(logoutRequest)
	if err != nil {
		t.Fatalf("parseSAMLLogoutRequest failed: %v", err)
	}
	if sessionIndex != "ST-123456-abcdef" {
		t.Errorf("Expected 'ST-123456-abcdef', got '%s'", sessionIndex)
	}
}

func TestParseSAMLLogoutRequest_AddNamespace(t *testing.T) {
	// 测试添加命名空间
	logoutRequest := `<samlp:LogoutRequest ID="123" Version="2.0">
		<samlp:SessionIndex>ST-123456-abcdef</samlp:SessionIndex>
	</samlp:LogoutRequest>`

	sessionIndex, err := parseSAMLLogoutRequest(logoutRequest)
	if err != nil {
		t.Fatalf("parseSAMLLogoutRequest failed: %v", err)
	}
	if sessionIndex != "ST-123456-abcdef" {
		t.Errorf("Expected 'ST-123456-abcdef', got '%s'", sessionIndex)
	}
}

func TestParseSAMLLogoutRequest_NoSessionIndex(t *testing.T) {
	// 测试没有SessionIndex
	logoutRequest := `<samlp:LogoutRequest xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol" ID="123" Version="2.0">
		<samlp:Reason>User logout</samlp:Reason>
	</samlp:LogoutRequest>`

	_, err := parseSAMLLogoutRequest(logoutRequest)
	if err == nil {
		t.Errorf("Expected error for missing SessionIndex")
	}
}
