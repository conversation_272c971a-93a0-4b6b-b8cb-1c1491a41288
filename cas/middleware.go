package cas

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
)

// UnauthorizedHandler 定义未授权请求的处理函数
// 返回的 bool 表示是否继续处理请求, true 则继续处理, false 直接返回
type UnauthorizedHandler func(w http.ResponseWriter, r *http.Request, e error) bool

// Middleware 提供CAS认证相关的中间件
type Middleware interface {
	// Auth 返回标准HTTP中间件，验证用户是否已登录，并将用户信息添加到请求上下文中
	Auth(next http.Handler) http.Handler

	// ForGin 返回Gin中间件，验证用户是否已登录，并将用户信息添加到Gin上下文中
	ForGin() gin.HandlerFunc

	// ForGoZero 返回Go-Zero中间件，验证用户是否已登录，并将用户信息添加到请求上下文中
	ForGoZero(next http.HandlerFunc) http.HandlerFunc

	// WithUnauthorizedHandler 设置未授权处理函数，返回中间件自身以支持链式调用
	WithUnauthorizedHandler(handler UnauthorizedHandler) Middleware
}

// middleware 实现AuthMiddleware接口
type middleware struct {
	sessionManager      SessionManager
	unauthorizedHandler UnauthorizedHandler
}

// NewMiddleware 创建一个新的认证中间件
func NewMiddleware(mgr SessionManager) Middleware {
	return &middleware{
		sessionManager: mgr,
		unauthorizedHandler: func(w http.ResponseWriter, r *http.Request, e error) bool {
			return true // 返回true表示继续处理请求
		},
	}
}

// WithUnauthorizedHandler 设置未授权处理函数
func (m *middleware) WithUnauthorizedHandler(handler UnauthorizedHandler) Middleware {
	m.unauthorizedHandler = handler
	return m
}

// Auth 实现通用的HTTP认证中间件
func (m *middleware) Auth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 从cookie中获取会话ID
		uuid := parseConsoleSessionUUID(r)
		if uuid == "" {
			if !m.unauthorizedHandler(w, r, nil) {
				return
			}
		}

		// 验证会话ID并获取用户信息
		user, err := m.sessionManager.GetLoginUser(r.Context(), uuid)
		// 将用户信息添加到请求上下文中
		ctx := context.WithValue(r.Context(), LoginUserContextKey, user)
		if (err != nil || user == nil || !user.Enabled) && !m.unauthorizedHandler(w, r, err) {
			return
		}

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// ForGin 返回Gin中间件，验证用户是否已登录，并将用户信息添加到Gin上下文中
func (m *middleware) ForGin() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从cookie中获取会话ID
		uuid := parseConsoleSessionUUID(c.Request)
		if uuid == "" && !m.unauthorizedHandler(c.Writer, c.Request, nil) {
			// 未找到会话ID，使用未授权处理函数
			c.Abort()
			return
		}

		// 验证会话ID并获取用户信息
		if uuid != "" {
			user, err := m.sessionManager.GetLoginUser(c.Request.Context(), uuid)
			// 将用户信息添加到Gin上下文和请求上下文中
			c.Set(LoginUserContextKey, user)
			c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), LoginUserContextKey, user))
			if (err != nil || user == nil || !user.Enabled) && !m.unauthorizedHandler(c.Writer, c.Request, err) {
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// ForGoZero 返回Go-Zero中间件，验证用户是否已登录，并将用户信息添加到请求上下文中
func (m *middleware) ForGoZero(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 从cookie中获取会话ID
		uuid := parseConsoleSessionUUID(r)
		if uuid == "" && !m.unauthorizedHandler(w, r, nil) {
			return
		}

		// 验证会话ID并获取用户信息
		ctx := context.Background()
		if uuid != "" {
			user, err := m.sessionManager.GetLoginUser(r.Context(), uuid)
			ctx = context.WithValue(r.Context(), LoginUserContextKey, user)
			if (err != nil || user == nil || !user.Enabled) && !m.unauthorizedHandler(w, r, err) {
				return
			}
		}

		// 将用户信息添加到请求上下文中
		next(w, r.WithContext(ctx))
	}
}

// GetLoginUser 从请求上下文中获取登录用户信息
func GetLoginUser(r *http.Request) *LoginUser {
	if r == nil {
		return nil
	}

	if user := r.Context().Value(LoginUserContextKey); user != nil {
		if loginUser, ok := user.(*LoginUser); ok {
			return loginUser
		}
	}
	return nil
}

// GetLoginUserFromGin 从Gin上下文中获取登录用户
func GetLoginUserFromGin(c *gin.Context) *LoginUser {
	if user, exists := c.Get(LoginUserContextKey); exists {
		if loginUser, ok := user.(*LoginUser); ok {
			return loginUser
		}
	}
	return nil
}
