package cas

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/coocood/freecache"
)

// memorySessionManager 使用FreeCache实现的会话管理器
// 提供自动过期功能，完全无GC开销
type memorySessionManager struct {
	// 用户缓存，UUID到登录用户
	users *freecache.Cache

	// 票据缓存，票据到UUID
	tickets *freecache.Cache

	// 互斥锁，用于在多缓存操作需要原子性的场景
	mu sync.RWMutex

	// 是否已关闭
	closed bool
}

// NewMemorySessionManager 创建新的基于freecache的会话管理器
func NewMemorySessionManager(size int) SessionManager {
	// 创建用户缓存
	userCache := freecache.NewCache(size)

	// 创建票据缓存（分配同样大小）
	ticketCache := freecache.NewCache(size)

	return &memorySessionManager{
		users:   userCache,
		tickets: ticketCache,
		closed:  false,
	}
}

// StoreLoginUser 在缓存中存储登录用户，并设置TTL
func (m *memorySessionManager) StoreLoginUser(_ context.Context, user *LoginUser) error {
	if user == nil || user.UUID == "" {
		return errors.New("无效的登录用户")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if m.closed {
		return errors.New("会话管理器已关闭")
	}

	// 对用户对象进行JSON序列化
	userData, err := json.Marshal(user)
	if err != nil {
		return fmt.Errorf("序列化用户数据失败: %w", err)
	}

	// 确定TTL
	var ttl int
	if user.SessionTTL > 0 {
		ttl = int(user.SessionTTL)
	}

	// 用户键(UUID -> 用户信息)
	userKey := uuidLoginUserPrefix + user.UUID

	// 存储用户数据
	err = m.users.Set([]byte(userKey), userData, ttl)
	if err != nil {
		return fmt.Errorf("存储用户数据失败: %w", err)
	}

	// 如果提供了票据，创建票据到UUID的映射
	if user.Ticket != "" {
		// 票据键(票据 -> UUID)
		ticketKey := userTicketUUIDPrefix + user.Ticket

		err = m.tickets.Set([]byte(ticketKey), []byte(user.UUID), ttl)
		if err != nil {
			// 如果存储票据映射失败，尝试回滚用户数据
			m.users.Del([]byte(userKey))
			return fmt.Errorf("存储票据映射失败: %w", err)
		}
	}

	return nil
}

// GetLoginUser 通过UUID检索登录用户
func (m *memorySessionManager) GetLoginUser(_ context.Context, uuid string) (*LoginUser, error) {
	if uuid == "" {
		return nil, errors.New("无效的UUID")
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.closed {
		return nil, errors.New("会话管理器已关闭")
	}

	// 用户键
	userKey := uuidLoginUserPrefix + uuid

	// 从缓存获取用户数据
	userData, err := m.users.Get([]byte(userKey))
	if err != nil {
		if errors.Is(err, freecache.ErrNotFound) {
			return nil, fmt.Errorf("找不到UUID对应的用户: %s", uuid)
		}
		return nil, fmt.Errorf("获取用户数据失败: %w", err)
	}

	// 反序列化用户数据
	var user LoginUser
	if err = json.Unmarshal(userData, &user); err != nil {
		return nil, fmt.Errorf("反序列化用户数据失败: %w", err)
	}

	return &user, nil
}

// DeleteLoginUser 通过UUID删除登录用户会话
func (m *memorySessionManager) DeleteLoginUser(_ context.Context, uuid string) error {
	if uuid == "" {
		return errors.New("无效的UUID")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if m.closed {
		return errors.New("会话管理器已关闭")
	}

	userKey := uuidLoginUserPrefix + uuid

	// 获取用户数据以找到票据
	userData, err := m.users.Get([]byte(userKey))
	if err != nil {
		if errors.Is(err, freecache.ErrNotFound) {
			// 用户不存在，无需操作
			return nil
		}
		return fmt.Errorf("获取用户数据失败: %w", err)
	}

	// 反序列化用户数据
	var user LoginUser
	if err = json.Unmarshal(userData, &user); err != nil {
		return fmt.Errorf("反序列化用户数据失败: %w", err)
	}

	// 删除用户
	m.users.Del([]byte(userKey))

	// 如果存在票据，删除票据映射
	if user.Ticket != "" {
		ticketKey := userTicketUUIDPrefix + user.Ticket
		m.tickets.Del([]byte(ticketKey))
	}

	return nil
}

// DeleteLoginUserByTicket 通过票据删除登录用户会话
func (m *memorySessionManager) DeleteLoginUserByTicket(_ context.Context, ticket string) error {
	if ticket == "" {
		return errors.New("无效的票据")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if m.closed {
		return errors.New("会话管理器已关闭")
	}

	ticketKey := userTicketUUIDPrefix + ticket

	// 获取票据对应的UUID
	uuidBytes, err := m.tickets.Get([]byte(ticketKey))
	if err != nil {
		if errors.Is(err, freecache.ErrNotFound) {
			// 票据不存在，无需操作
			return nil
		}
		return fmt.Errorf("获取票据映射失败: %w", err)
	}

	uuid := string(uuidBytes)
	userKey := uuidLoginUserPrefix + uuid

	// 删除用户
	m.users.Del([]byte(userKey))

	// 删除票据映射
	m.tickets.Del([]byte(ticketKey))

	return nil
}

// Close 关闭会话管理器
func (m *memorySessionManager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.closed {
		return nil // 已关闭，直接返回
	}

	// freecache 没有明确的关闭方法，标记为已关闭即可
	m.closed = true

	return nil
}

// RefreshUserSession 刷新用户会话的过期时间
func (m *memorySessionManager) RefreshUserSession(_ context.Context, uuid string,
	user *LoginUser, ttl time.Duration) (err error) {
	if uuid == "" {
		return errors.New("无效的UUID")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if m.closed {
		return errors.New("会话管理器已关闭")
	}

	// 用户键
	userKey := uuidLoginUserPrefix + uuid

	// 转换TTL为秒
	ttlSeconds := int(ttl.Seconds())
	if ttlSeconds <= 0 {
		ttlSeconds = int(user.SessionTTL) // 使用原始TTL
	}

	// 重新存储用户数据以刷新TTL
	userData, _ := json.Marshal(user)
	if err = m.users.Set([]byte(userKey), userData, ttlSeconds); err != nil {
		return fmt.Errorf("刷新用户数据TTL失败: %w", err)
	}

	// 如果存在票据，也刷新票据映射的TTL
	if user.Ticket != "" {
		ticketKey := userTicketUUIDPrefix + user.Ticket
		// 重新设置票据到UUID的映射，刷新TTL
		err = m.tickets.Set([]byte(ticketKey), []byte(uuid), ttlSeconds)
		if err != nil {
			return fmt.Errorf("刷新票据TTL失败: %w", err)
		}
	}

	return nil
}
