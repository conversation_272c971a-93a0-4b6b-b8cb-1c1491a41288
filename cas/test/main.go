package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/redis/go-redis/v9"

	"work.ctyun.cn/git/console-common/sso-go-sdk/cas"
)

// 示例配置
var (
	// 服务器地址和端口
	serverAddr = ":8080"

	// 使用Redis作为会话存储
	useRedis      = true
	redisAddr     = "localhost:26379"
	redisPassword = "ci"

	// CAS服务器配置
	casServerHost           = "https://wwwtest.ctyun.cn:21443" // https://wwwtest.ctyun.cn:21443
	casServerTicketValidate = "cas/serviceValidate"

	// CAS客户端配置
	// https://dev.consoletest.ctyun.cn
	casClientHost = "http://consoletest.ctyun.cn" // 不带 dev 前缀是因为 testbcp 默认跳转域名没有 dev, 这里测试代理登录
	casAppID      = "152999073894506103"
	casAppSecret  = "IvKWNwM6UE4dqRXeF2GZcPJi5ynr17BbTx3m0ahCAL9"
	consoleIndex  = "/console/index/"
)

func main() {
	// 设置日志
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	log.Println("启动CAS示例服务...")

	// 读取环境变量（如果存在）
	if envAddr := os.Getenv("SERVER_ADDR"); envAddr != "" {
		serverAddr = envAddr
	}
	if envRedis := os.Getenv("USE_REDIS"); envRedis == "true" {
		useRedis = true
	}
	if envRedisAddr := os.Getenv("REDIS_ADDR"); envRedisAddr != "" {
		redisAddr = envRedisAddr
	}
	if envRedisPassword := os.Getenv("REDIS_PASSWORD"); envRedisPassword != "" {
		redisPassword = envRedisPassword
	}

	// 创建CAS配置
	config := createCasConfig()

	// 创建会话管理器
	sessionManager, err := createSessionManager()
	if err != nil {
		log.Fatalf("创建会话管理器失败: %v", err)
	}

	// 注册路由
	registerRoutes(config, sessionManager)

	// 启动HTTP服务器
	log.Printf("服务器启动在 %s", serverAddr)
	if err := http.ListenAndServe(serverAddr, nil); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}

// createCasConfig 创建CAS配置
func createCasConfig() *cas.Config {
	// 设置客户端配置
	clientConfig := cas.DefaultClientConfig()
	clientConfig.Host = casClientHost
	clientConfig.AppId = casAppID
	clientConfig.AppSecret = casAppSecret
	clientConfig.ConsoleIndex = consoleIndex

	// 设置服务器配置
	serverConfig := cas.DefaultServerConfig()
	serverConfig.Host = casServerHost
	serverConfig.TicketValidate = casServerTicketValidate

	// 创建配置
	return &cas.Config{
		Client: clientConfig,
		Server: serverConfig,
	}
}

// createSessionManager 创建会话管理器（内存或Redis）
func createSessionManager() (cas.SessionManager, error) {
	if useRedis {
		// 使用Redis作为会话存储
		log.Println("使用Redis作为会话存储")
		return cas.NewRedisSessionManager(
			cas.WithRedisClient(
				// 创建Redis客户端
				// 注意：在实际环境中应该处理连接失败的情况
				redis.NewClient(&redis.Options{
					Addr:     redisAddr,
					Password: redisPassword,
				}),
			),
		)
	}

	// 使用内存作为会话存储（默认）
	log.Println("使用内存作为会话存储")
	return cas.NewMemorySessionManager(1024 * 1024), nil // 1MB缓存
}

// registerRoutes 注册HTTP路由
func registerRoutes(config *cas.Config, sessionManager cas.SessionManager) {
	// 创建CAS处理器
	handler := cas.NewHandler(config, sessionManager,
		// 可选：自定义成功处理器
		cas.WithSuccessHandler(func(w http.ResponseWriter, r *http.Request) {
			log.Println("CAS操作成功")
		}),
		// 可选：自定义错误处理器
		cas.WithErrorHandler(func(w http.ResponseWriter, r *http.Request, err error) {
			log.Printf("CAS错误: %v", err)
		}),
	)

	// 创建中间件
	authMiddleware := cas.NewMiddleware(sessionManager).WithUnauthorizedHandler(
		// 自定义未授权处理函数
		func(w http.ResponseWriter, r *http.Request, err error) bool {
			// 对于API请求返回401状态码
			if r.Header.Get("Accept") == "application/json" {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				json.NewEncoder(w).Encode(map[string]string{
					"error": "未授权，请先登录",
				})
				return false // 不继续处理请求
			}

			// 对于普通请求重定向到登录页面
			redirectURL := fmt.Sprintf("/login?redirectUrl=%s", r.URL.Path)
			http.Redirect(w, r, redirectURL, http.StatusFound)
			return false // 不继续处理请求
		},
	)

	// 注册CAS相关的路由
	http.HandleFunc("/login", handler.Login())           // CAS登录
	http.HandleFunc("/logout", handler.Logout())         // CAS登出
	http.HandleFunc("/delegate", handler.DelegateSign()) // 委托登录
	http.HandleFunc("/agent", handler.LoginFromAgent())  // 代理登录

	// 注册首页路由 - 无需认证
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// 检查用户是否已登录
		user, _ := sessionManager.GetLoginUser(r.Context(), parseConsoleSessionUUID(r))
		if user != nil && user.Enabled {
			// 用户已登录，显示用户信息
			w.Header().Set("Content-Type", "text/html; charset=utf-8")
			fmt.Fprintf(w, `
				<h1>欢迎访问CAS示例</h1>
				<p>您已登录为: %s</p>
				<p>用户ID: %s</p>
				<p>账户ID: %s</p>
				<p>邮箱: %s</p>
				<p><a href="/logout">登出</a></p>
				<p><a href="/protected">访问受保护的页面</a></p>
			`,
				user.AuthUserID.Name,
				user.AuthUserID.UserID,
				user.AuthUserID.AccountID,
				user.AuthUserID.Email,
			)
		} else {
			// 用户未登录，显示登录链接
			w.Header().Set("Content-Type", "text/html; charset=utf-8")
			fmt.Fprintf(w, `
				<h1>欢迎访问CAS示例</h1>
				<p>您尚未登录</p>
				<p><a href="/login">登录</a></p>
			`)
		}
	})

	// 注册受保护的路由 - 需要认证
	protectedHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 用户已通过认证，可以访问此页面
		user := cas.GetLoginUser(r)
		if user == nil || !user.Enabled {
			// 这种情况不应该发生，因为中间件已经处理了未授权的情况
			http.Error(w, "未授权", http.StatusUnauthorized)
			return
		}

		// 显示受保护的内容
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		fmt.Fprintf(w, `
			<h1>受保护的页面</h1>
			<p>您已成功访问受保护的资源</p>
			<p>用户名: %s</p>
			<p><a href="/">返回首页</a></p>
			<p><a href="/logout">登出</a></p>
		`, user.AuthUserID.Name)
	})

	// 应用认证中间件到受保护的路由
	http.Handle("/protected", authMiddleware.Auth(protectedHandler))

	// 添加一个API路由示例 - 需要认证
	apiHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 用户已通过认证，可以访问此API
		user := cas.GetLoginUser(r)
		if user == nil || !user.Enabled {
			// 这种情况不应该发生，因为中间件已经处理了未授权的情况
			http.Error(w, "未授权", http.StatusUnauthorized)
			return
		}

		// 返回用户信息的JSON
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"user": map[string]interface{}{
				"name":      user.AuthUserID.Name,
				"userId":    user.AuthUserID.UserID,
				"accountId": user.AuthUserID.AccountID,
				"email":     user.AuthUserID.Email,
			},
		})
	})

	// 应用认证中间件到API路由
	http.Handle("/api/user", authMiddleware.Auth(apiHandler))
}

func parseConsoleSessionUUID(r *http.Request) string {
	for _, cookie := range r.Cookies() {
		if strings.EqualFold(cookie.Name, "consolesessionid") {
			return cookie.Value
		}
	}
	// 没有找到有效的Cookie
	return ""
}
