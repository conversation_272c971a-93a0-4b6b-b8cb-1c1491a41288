package cas

import (
	"context"
	"testing"
	"time"
)

func TestMemorySessionManager_CRUD(t *testing.T) {
	// 创建内存会话管理器
	cacheSize := 10 * 1024 * 1024 // 10MB
	manager := NewMemorySessionManager(cacheSize)

	ctx := context.Background()

	// 测试：创建登录用户
	user := &LoginUser{
		UUID:       "test-uuid",
		Ticket:     "ST-********",
		Enabled:    true,
		SessionTTL: 3600,
		AuthUserID: &AuthUserID{
			UserID:    "test-user",
			AccountID: "test-account",
			Email:     "<EMAIL>",
			Name:      "Test User",
		},
	}

	// 存储用户
	err := manager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("StoreLoginUser() error = %v", err)
	}

	// 通过UUID获取用户
	retrievedUser, err := manager.GetLoginUser(ctx, user.UUID)
	if err != nil {
		t.Fatalf("GetLoginUser() error = %v", err)
	}

	// 验证检索到的用户数据
	if retrievedUser.UUID != user.UUID {
		t.Errorf("检索到的用户UUID不匹配, got = %v, want = %v", retrievedUser.UUID, user.UUID)
	}
	if retrievedUser.Ticket != user.Ticket {
		t.Errorf("检索到的用户Ticket不匹配, got = %v, want = %v", retrievedUser.Ticket, user.Ticket)
	}
	if retrievedUser.AuthUserID == nil {
		t.Fatal("检索到的用户AuthUserID为空")
	}
	if retrievedUser.AuthUserID.UserID != user.AuthUserID.UserID {
		t.Errorf("检索到的用户UserID不匹配, got = %v, want = %v", retrievedUser.AuthUserID.UserID, user.AuthUserID.UserID)
	}

	// 测试：通过票据删除用户
	err = manager.DeleteLoginUserByTicket(ctx, user.Ticket)
	if err != nil {
		t.Fatalf("DeleteLoginUserByTicket() error = %v", err)
	}

	// 验证用户已被删除
	_, err = manager.GetLoginUser(ctx, user.UUID)
	if err == nil {
		t.Error("GetLoginUser() error = nil, 期望错误表示用户不存在")
	}

	// 测试：创建新用户并通过UUID删除
	user2 := &LoginUser{
		UUID:       "test-uuid-2",
		Ticket:     "ST-87654321",
		Enabled:    true,
		SessionTTL: 3600,
	}
	err = manager.StoreLoginUser(ctx, user2)
	if err != nil {
		t.Fatalf("StoreLoginUser() error = %v", err)
	}

	// 通过UUID删除用户
	err = manager.DeleteLoginUser(ctx, user2.UUID)
	if err != nil {
		t.Fatalf("DeleteLoginUser() error = %v", err)
	}

	// 验证用户已被删除
	_, err = manager.GetLoginUser(ctx, user2.UUID)
	if err == nil {
		t.Error("GetLoginUser() error = nil, 期望错误表示用户不存在")
	}
}

func TestMemorySessionManager_TTL(t *testing.T) {
	// 创建内存会话管理器
	cacheSize := 10 * 1024 * 1024 // 10MB
	manager := NewMemorySessionManager(cacheSize)

	ctx := context.Background()

	// 创建短TTL的用户
	user := &LoginUser{
		UUID:       "ttl-test-uuid",
		Ticket:     "ST-ttl-test",
		Enabled:    true,
		SessionTTL: 2, // 2秒过期
	}

	// 存储用户
	err := manager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("StoreLoginUser() error = %v", err)
	}

	// 立即验证用户可以被检索
	_, err = manager.GetLoginUser(ctx, user.UUID)
	if err != nil {
		t.Fatalf("立即获取用户失败: %v", err)
	}

	// 等待TTL过期
	time.Sleep(3 * time.Second)

	// 验证用户已过期
	_, err = manager.GetLoginUser(ctx, user.UUID)
	if err == nil {
		t.Error("GetLoginUser() error = nil, 期望错误表示用户已过期")
	}
}

// TestMemorySessionManager_RefreshSession 测试刷新会话TTL的功能
func TestMemorySessionManager_RefreshSession(t *testing.T) {
	// 创建内存会话管理器
	cacheSize := 10 * 1024 * 1024 // 10MB
	manager := NewMemorySessionManager(cacheSize)

	ctx := context.Background()

	// 创建短TTL的用户
	user := &LoginUser{
		UUID:       "refresh-test-uuid",
		Ticket:     "ST-refresh-test",
		Enabled:    true,
		SessionTTL: 2, // 2秒过期
	}

	// 存储用户
	err := manager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("StoreLoginUser() error = %v", err)
	}

	// 等待1秒（TTL的一半）
	time.Sleep(1 * time.Second)

	// 刷新会话，设置更长的TTL
	newTTL := 5 * time.Second
	err = manager.RefreshUserSession(ctx, user.UUID, user, newTTL)
	if err != nil {
		t.Fatalf("RefreshUserSession() error = %v", err)
	}

	// 再等待2秒（原始TTL已过期，但新TTL未过期）
	time.Sleep(2 * time.Second)

	// 验证用户仍然可以被检索
	refreshedUser, err := manager.GetLoginUser(ctx, user.UUID)
	if err != nil {
		t.Fatalf("刷新后获取用户失败: %v", err)
	}

	if refreshedUser == nil {
		t.Fatal("刷新后用户不应为nil")
	}

	// 验证用户数据保持不变
	if refreshedUser.UUID != user.UUID {
		t.Errorf("刷新后用户UUID不匹配, got = %v, want = %v", refreshedUser.UUID, user.UUID)
	}
	if refreshedUser.Ticket != user.Ticket {
		t.Errorf("刷新后用户Ticket不匹配, got = %v, want = %v", refreshedUser.Ticket, user.Ticket)
	}

	// 等待剩余的刷新TTL过期
	time.Sleep(3 * time.Second)

	// 验证用户现在已过期
	_, err = manager.GetLoginUser(ctx, user.UUID)
	if err == nil {
		t.Error("GetLoginUser() error = nil, 期望错误表示刷新后的用户已过期")
	}
}
