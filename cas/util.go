package cas

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
)

func escapeFragment(s string) string {
	// 把 s 当作 URL 的 Fragment 部分，调用 EscapedFragment()
	// 只会对 RFC3986 里不允许出现在 fragment 里的字符做百分号转义
	return (&url.URL{Fragment: s}).EscapedFragment()
}

// joinURL 正确拼接URL路径，确保路径之间有正确的分隔符
func joinURL(base, path string) string {
	// 如果base为空，直接返回path
	if base == "" {
		return path
	}

	// 如果path为空，直接返回base
	if path == "" {
		return base
	}

	// 确保base不以/结尾，path以/开头
	base = strings.TrimSuffix(base, "/")

	// 如果path不以/开头，添加/
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}

	return base + path
}

// isURLContainsQuery 检查URL是否包含查询参数
func isURLContainsQuery(urlStr string) bool {
	// 如果URL为空，那么不包含查询参数
	if urlStr == "" {
		return false
	}

	// 尝试解析URL
	u, err := url.Parse(urlStr)
	if err != nil {
		// 解析失败，保守返回false
		return false
	}

	// 检查URL是否有查询部分
	return u.RawQuery != ""
}

// getTextForElement 从XML字符串中提取指定元素的文本内容
func getTextForElement(xmlStr string, elementName string) (string, error) {
	if xmlStr == "" || elementName == "" {
		return "", fmt.Errorf("XML字符串或元素名称为空")
	}

	decoder := xml.NewDecoder(bytes.NewReader([]byte(xmlStr)))
	decoder.Strict = false // 允许一些XML解析宽容度

	// 用于存储找到的元素内容
	var content string
	var found bool
	var inTargetElement bool
	var depth int

	for {
		token, err := decoder.Token()
		if err == io.EOF {
			break
		}
		if err != nil {
			return "", fmt.Errorf("解析XML失败: %w", err)
		}

		switch se := token.(type) {
		case xml.StartElement:
			// 检查本地名称是否匹配，忽略XML命名空间
			if se.Name.Local == elementName {
				inTargetElement = true
				depth = 1
			} else if inTargetElement {
				// 跟踪嵌套深度
				depth++
			}
		case xml.EndElement:
			if se.Name.Local == elementName && inTargetElement {
				inTargetElement = false
				found = true
			} else if inTargetElement {
				depth--
			}
		case xml.CharData:
			// 只有当我们在目标元素内且深度为1时才记录内容
			// 深度为1表示直接在目标元素内，而不是其子元素内
			if inTargetElement && depth == 1 {
				content = string(se)
			}
		}

		// 一旦找到目标元素并处理完毕，就可以退出循环
		if found {
			break
		}
	}

	if !found {
		return "", fmt.Errorf("未找到元素 %s", elementName)
	}

	return content, nil
}

// parseSAMLLogoutRequest 从CAS SAML登出请求中提取SessionIndex
func parseSAMLLogoutRequest(logoutRequest string) (string, error) {
	// 如果缺少XML命名空间，添加它们
	if !bytes.Contains([]byte(logoutRequest), []byte("xmlns:samlp=\"urn:oasis:names:tc:SAML:2.0:protocol\"")) {
		logoutRequest = strings.ReplaceAll(
			logoutRequest,
			"<samlp:LogoutRequest",
			"<samlp:LogoutRequest xmlns:samlp=\"urn:oasis:names:tc:SAML:2.0:protocol\" xmlns:saml=\"urn:oasis:names:tc:SAML:2.0:assertion\"",
		)
	}

	// 提取SessionIndex元素的内容
	sessionIndex, err := getTextForElement(logoutRequest, "SessionIndex")
	if err != nil {
		return "", fmt.Errorf("提取SessionIndex失败: %w", err)
	}

	return sessionIndex, nil
}

// parseConsoleSessionUUID 从请求的Cookie中解析UUID
func parseConsoleSessionUUID(r *http.Request) string {
	for _, cookie := range r.Cookies() {
		if strings.EqualFold(cookie.Name, "consolesessionid") {
			return cookie.Value
		}
	}
	// 没有找到有效的Cookie
	return ""
}
