package cas

import (
	"net/http"
	"net/url"

	"github.com/samber/mo"
	"gopkg.in/cas.v2"
)

type logoutHandler struct {
	*service
}

func LogoutHandler(config *Config, sessionManager SessionManager) http.HandlerFunc {
	hdr := &logoutHandler{
		service: &service{
			clientConfig:   config.Client,
			serverConfig:   config.Server,
			sessionManager: sessionManager,
			casClient: newClientWrapper(&cas.Options{
				URL: mo.TupleToResult(url.Parse(config.Server.Host)).MustGet(),
			}),
		},
	}
	return hdr.Handle
}

// Handle 处理CAS请求
func (l *logoutHandler) Handle(w http.ResponseWriter, r *http.Request) {
	// 从请求中解析UUID
	sessionId := parseConsoleSessionUUID(r)

	// 如果存在UUID，则删除会话并禁用Cookie
	var err error
	if sessionId != "" {
		err = l.sessionManager.DeleteLoginUser(r.Context(), sessionId)
		l.disableConsoleCookie(w)
	}

	// 构建服务URL（指向控制台首页）
	serviceUrl := joinURL(l.clientConfig.Host, l.clientConfig.ConsoleIndex)

	// 构建登出URL
	newUrl := joinURL(l.serverConfig.Host, l.serverConfig.Logout) + "?service=" + url.QueryEscape(serviceUrl)

	// 执行重定向
	http.Redirect(w, r, newUrl, http.StatusFound)
	if err != nil {
		l.handleError(w, r, err)
	} else {
		l.handleSuccess(w, r)
	}
}
