package cas

import (
	"context"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
)

// TestLoginMiddlewareHandle_WithTicket 测试处理带有有效票据的请求
func TestLoginHandlerHandle_WithTicket(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LoginHandler(config, sessionManager)

	// 使用gomonkey模拟http.Client.do方法，用于模拟CAS服务器的响应
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个成功的响应
			return createTestSuccessResponse()
		})
	defer patch.Reset()

	// 创建请求，包含有效票据
	r := httptest.NewRequest("GET", "http://client.example.com/login?ticket=ST-1234-valid", nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 对于有效票据，应该重定向到控制台首页
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 应该重定向到控制台首页
	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Client.ConsoleIndex) {
		t.Errorf("Expected location to start with %q, got %q", config.Client.ConsoleIndex, location)
	}

	// 应该设置会话Cookie
	var sessionCookie *http.Cookie
	for _, cookie := range rsp.Cookies() {
		if cookie.Name == "consolesessionid" {
			sessionCookie = cookie
			break
		}
	}

	if sessionCookie == nil {
		t.Error("Expected consolesessionid cookie to be set")
	} else {
		// 验证会话用户是否被存储
		user, err := sessionManager.GetLoginUser(context.Background(), sessionCookie.Value)
		if err != nil {
			t.Errorf("Failed to get user: %v", err)
		}
		if user == nil {
			t.Error("Expected user to be stored in session")
		} else {
			// 验证用户信息是否正确
			if user.AuthUserID.UserID != "test-user-id" {
				t.Errorf("Expected user ID to be 'test-user-id', got %q", user.AuthUserID.UserID)
			}

			// 验证TTL是否正确设置
			expectedTTL := int64(30 * 60) // 30分钟转换为秒
			if user.SessionTTL != expectedTTL {
				t.Errorf("Expected SessionTTL to be %d seconds, got %d", expectedTTL, user.SessionTTL)
				// 如果SessionTTL为0，可能是属性解析问题，记录详细信息
				if user.SessionTTL == 0 {
					t.Logf("SessionTTL为0，可能是CAS属性解析问题")
				}
			}
		}
	}
}

// TestLoginHandlerHandle_WithInvalidTicket 测试处理带有无效票据的请求
func TestLoginHandlerHandle_WithInvalidTicket(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LoginHandler(config, sessionManager)

	// 使用gomonkey模拟http.Client.do方法
	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个失败的响应
			return createTestFailureResponse("INVALID_TICKET", "Ticket ST-1234-invalid not recognized")
		})
	defer patch.Reset()

	// 创建请求，包含无效票据
	r := httptest.NewRequest("GET", "http://client.example.com/login?ticket=ST-1234-invalid", nil)
	w := httptest.NewRecorder()

	// 处理请求（由于票据无效，应该重定向到登录页面）
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到登录页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 检查是否重定向到CAS服务器的登录页面
	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Server.Host+config.Server.Login) {
		t.Errorf("Expected Location to start with %q, got %q", config.Server.Host+config.Server.Login, location)
	}

	// 验证没有设置会话Cookie
	for _, cookie := range rsp.Cookies() {
		if cookie.Name == "consolesessionid" && cookie.Value != "" && cookie.MaxAge > 0 {
			t.Error("Unexpected session cookie found for invalid ticket")
		}
	}
}

// TestLoginHandlerLogin_ValidationFailed 测试票据验证失败的情况
func TestLoginHandlerLogin_ValidationFailed(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LoginHandler(config, sessionManager)

	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个过期票据的失败响应
			return createTestFailureResponse("INVALID_TICKET", "Ticket ST-1234-expired has expired")
		})
	defer patch.Reset()

	// 创建请求，使用可能会导致验证失败的票据
	r := httptest.NewRequest("GET", "http://client.example.com/login?ticket=ST-1234-expired", nil)
	w := httptest.NewRecorder()

	// 处理请求（由于票据验证失败，应该重定向到登录页面）
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到登录页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 检查是否重定向到CAS服务器的登录页面
	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Server.Host+config.Server.Login) {
		t.Errorf("Expected Location to start with %q, got %q", config.Server.Host+config.Server.Login, location)
	}

	// 验证没有设置会话Cookie
	for _, cookie := range rsp.Cookies() {
		if cookie.Name == "consolesessionid" && cookie.Value != "" && cookie.MaxAge > 0 {
			t.Error("Unexpected session cookie found for expired ticket")
		}
	}
}

// TestLoginHandlerCASAttributes 测试从CAS响应中提取所有必要的用户属性
func TestLoginHandlerCASAttributes(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建处理函数
	handler := LoginHandler(config, sessionManager)

	patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&http.Client{}), "do",
		func(client *http.Client, req *http.Request) (*http.Response, error) {
			// 返回一个成功的响应，包含完整用户属性
			return createTestSuccessResponse()
		})
	defer patch.Reset()

	// 创建请求，使用有效票据
	r := httptest.NewRequest("GET", "http://client.example.com/login?ticket=ST-1234-valid", nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 获取cookie
	var sessionCookie *http.Cookie
	for _, cookie := range rsp.Cookies() {
		if cookie.Name == "consolesessionid" {
			sessionCookie = cookie
			break
		}
	}

	if sessionCookie == nil {
		t.Error("Expected consolesessionid cookie to be set")
		return
	}

	// 验证会话用户是否被存储
	user, err := sessionManager.GetLoginUser(context.Background(), sessionCookie.Value)
	if err != nil {
		t.Errorf("Failed to get user: %v", err)
		return
	}
	if user == nil {
		t.Error("Expected user to be stored in session")
		return
	}

	// 验证TTL是否正确设置
	expectedTTL := int64(30 * 60) // 30分钟转换为秒
	if user.SessionTTL != expectedTTL {
		t.Errorf("Expected SessionTTL to be %d seconds, got %d", expectedTTL, user.SessionTTL)
	}

	// 验证用户基本属性
	if user.UID != "test-uid" {
		t.Errorf("Expected UID to be %q, got %q", "test-uid", user.UID)
	}

	// 验证AuthUserID属性
	if user.AuthUserID == nil {
		t.Fatal("Expected AuthUserID to be non-nil")
	}
	if user.AuthUserID.UserID != "test-user-id" {
		t.Errorf("Expected AuthUserID.UserID to be %q, got %q", "test-user-id", user.AuthUserID.UserID)
	}
	if user.AuthUserID.AccountID != "test-domain-id" {
		t.Errorf("Expected AuthUserID.AccountID to be %q, got %q", "test-domain-id", user.AuthUserID.AccountID)
	}
	if user.AuthUserID.Email != "<EMAIL>" {
		t.Errorf("Expected AuthUserID.Email to be %q, got %q", "<EMAIL>", user.AuthUserID.Email)
	}
	if user.AuthUserID.Name != "Test User" {
		t.Errorf("Expected AuthUserID.Name to be %q, got %q", "Test User", user.AuthUserID.Name)
	}
	if user.AuthUserID.UserType != "normal" {
		t.Errorf("Expected AuthUserID.UserType to be %q, got %q", "normal", user.AuthUserID.UserType)
	}
	if user.AuthUserID.RealName != "Real Test User" {
		t.Errorf("Expected AuthUserID.RealName to be %q, got %q", "Real Test User", user.AuthUserID.RealName)
	}
	if user.AuthUserID.City != "Test City" {
		t.Errorf("Expected AuthUserID.City to be %q, got %q", "Test City", user.AuthUserID.City)
	}

	// 验证IsRoot字段是否根据用户ID和根用户ID的比较正确设置
	// 在我们的测试数据中，用户ID与根用户ID不同，因此IsRoot应为"0"
	if user.AuthUserID.IsRoot != "0" {
		t.Errorf("Expected IsRoot to be %q, got %q", "0", user.AuthUserID.IsRoot)
	}
}

// TestLoginHandlerHandle_SessionRefresh 测试会话刷新
func TestLoginHandlerHandle_SessionRefresh(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 创建一个用户并存储到会话中
	ctx := context.Background()
	uuid := "test-refresh-uuid"
	user := &LoginUser{
		UUID:       uuid,
		Ticket:     "ST-1234-refresh-test",
		Enabled:    true,
		SessionTTL: 2,
		AuthUserID: &AuthUserID{
			UserID:    "test-user-id",
			AccountID: "test-account-id",
			Email:     "<EMAIL>",
			Name:      "Test User",
		},
	}

	// 存储用户
	err := sessionManager.StoreLoginUser(ctx, user)
	if err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建一个带有会话Cookie的请求，模拟已经登录的用户请求
	r := httptest.NewRequest("GET", "http://client.example.com/home", nil)
	cookie := &http.Cookie{
		Name:  "consolesessionid",
		Value: uuid,
	}
	r.AddCookie(cookie)

	// 手动验证会话
	hdr := &loginHandler{
		service: &service{
			clientConfig:   config.Client,
			serverConfig:   config.Server,
			sessionManager: sessionManager,
		},
	}

	time.Sleep(time.Second)
	// 验证并刷新会话
	refreshedUser, err := hdr.service.validateSessionId(r.Context(), user.UUID)
	if err != nil {
		t.Fatalf("Failed to validate session: %v", err)
	}

	// 验证会话已刷新
	if refreshedUser == nil {
		t.Fatal("Expected refreshed user to be non-nil")
	}
	if refreshedUser.UUID != user.UUID {
		t.Errorf("Expected refreshed user ID to be %q, got %q", user.UUID, refreshedUser.UUID)
	}

	// 确保用户仍然可以从会话中检索
	retrievedUser, err := sessionManager.GetLoginUser(context.Background(), user.UUID)
	if err != nil {
		t.Errorf("Failed to retrieve user after refresh: %v", err)
	}
	if retrievedUser == nil {
		t.Error("Expected user to still be in session after refresh")
		return
	}
}

// TestLoginHandlerHandle_LogoutRequest 测试SAML登出请求
func TestLoginHandlerHandle_LogoutRequest(t *testing.T) {
	// 创建配置和会话管理器
	config := createTestConfig()
	sessionManager := NewMemorySessionManager(1024 * 1024)

	// 存储一个测试用户
	ctx := context.Background()
	testTicket := "ST-1234-logout-test"
	uuid := "test-logout-uuid"
	user := &LoginUser{
		UUID:       uuid,
		Ticket:     testTicket,
		Enabled:    true,
		SessionTTL: 3600,
	}
	if err := sessionManager.StoreLoginUser(ctx, user); err != nil {
		t.Fatalf("Failed to store user: %v", err)
	}

	// 创建处理函数
	handler := LoginHandler(config, sessionManager)

	// 构造SAML登出请求
	samlRequest := `<samlp:LogoutRequest xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol" xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion">
		<saml:NameID>test-user</saml:NameID>
		<samlp:SessionIndex>` + testTicket + `</samlp:SessionIndex>
	</samlp:LogoutRequest>`

	// 创建请求，包含登出请求
	r := httptest.NewRequest("GET", "http://client.example.com/login?logoutRequest="+url.QueryEscape(samlRequest), nil)
	w := httptest.NewRecorder()

	// 处理请求
	handler(w, r)

	// 验证响应
	rsp := w.Result()
	defer rsp.Body.Close()

	// 应该重定向到登录页面
	if rsp.StatusCode != http.StatusFound {
		t.Errorf("Expected status code %d, got %d", http.StatusFound, rsp.StatusCode)
	}

	// 应该重定向到CAS服务器的登录页面
	location := rsp.Header.Get("Location")
	if !strings.HasPrefix(location, config.Server.Host+config.Server.Login) {
		t.Errorf("Expected Location to start with %q, got %q", config.Server.Host+config.Server.Login, location)
	}

	// 获取cookie
	var sessionCookie *http.Cookie
	for _, cookie := range rsp.Cookies() {
		if cookie.Name == "consolesessionid" {
			sessionCookie = cookie
			break
		}
	}

	if sessionCookie == nil {
		t.Error("Expected consolesessionid cookie to be set")
		return
	}

	if sessionCookie.MaxAge != 0 {
		t.Errorf("Expected consolesessionid cookie MaxAge to be 0, got %d", sessionCookie.MaxAge)
	}

	user, err := sessionManager.GetLoginUser(ctx, uuid)
	if err == nil {
		t.Error("Expected got err: 找不到UUID对应的用户")
		return
	}
	if user != nil {
		t.Error("Expected user not to be stored in session")
		return
	}
}
