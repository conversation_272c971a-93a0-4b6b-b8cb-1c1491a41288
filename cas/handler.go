package cas

import (
	"net/http"
	"net/url"

	"github.com/samber/mo"
	"gopkg.in/cas.v2"
)

// Handler 提供CAS认证相关的HTTP处理器
type Handler interface {
	// Login 返回处理CAS登录的HTTP处理器
	Login() http.HandlerFunc

	// Logout 返回处理CAS登出的HTTP处理器
	Logout() http.HandlerFunc

	// DelegateSign 返回处理委托签名的HTTP处理器
	DelegateSign() http.HandlerFunc

	// LoginFromAgent 返回处理代理登录的HTTP处理器
	LoginFromAgent() http.HandlerFunc
}

// HandlerOption 配置CasHandler的选项
type HandlerOption func(*handlerOptions)

// 处理器选项
type handlerOptions struct {
	errorHandler   func(http.ResponseWriter, *http.Request, error)
	successHandler http.HandlerFunc
}

// WithSuccessHandler 设置成功处理函数
func WithSuccessHandler(handler http.HandlerFunc) HandlerOption {
	return func(o *handlerOptions) {
		o.successHandler = handler
	}
}

// WithErrorHandler 设置错误处理函数
func WithErrorHandler(handler func(http.ResponseWriter, *http.Request, error)) HandlerOption {
	return func(o *handlerOptions) {
		o.errorHandler = handler
	}
}

// casHandler 实现CasHandler接口
type casHandler struct {
	login        *loginHandler
	logout       *logoutHandler
	delegateSign *delegateSignHandler
}

// NewHandler 创建一个新的CAS处理器
func NewHandler(config *Config, mgr SessionManager, opts ...HandlerOption) Handler {
	options := &handlerOptions{}
	for _, opt := range opts {
		opt(options)
	}

	svc := &service{
		clientConfig:   config.Client,
		serverConfig:   config.Server,
		sessionManager: mgr,
		errorHandler:   options.errorHandler,
		successHandler: options.successHandler,
		casClient: newClientWrapper(&cas.Options{
			URL: mo.TupleToResult(url.Parse(config.Server.Host)).MustGet(),
		}),
	}

	return &casHandler{
		login:        &loginHandler{service: svc},
		logout:       &logoutHandler{service: svc},
		delegateSign: &delegateSignHandler{service: svc},
	}
}

// Login 返回处理CAS登录的HTTP处理器
func (h *casHandler) Login() http.HandlerFunc {
	return h.login.Handle
}

// Logout 返回处理CAS登出的HTTP处理器
func (h *casHandler) Logout() http.HandlerFunc {
	return h.logout.Handle
}

// DelegateSign 返回处理委托签名的HTTP处理器
func (h *casHandler) DelegateSign() http.HandlerFunc {
	return h.delegateSign.Handle
}

// LoginFromAgent 返回处理代理登录的HTTP处理器
func (h *casHandler) LoginFromAgent() http.HandlerFunc {
	return h.login.HandleLoginFromAgent
}
