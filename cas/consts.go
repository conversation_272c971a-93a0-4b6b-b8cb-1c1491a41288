package cas

// API响应相关常量
const (
	// OpenapiSuccessCode API成功返回码
	OpenapiSuccessCode = 800

	// OpenapiFailCode API失败返回码
	OpenapiFailCode = 900

	// DelegateSignErrorCode 委托签名错误码
	DelegateSignErrorCode = "Delegate.Sign.Error"

	// DelegateSignErrorMsg 委托签名错误信息
	DelegateSignErrorMsg = "delegate sign error"
)

const (
	// LoginUserContextKey 用于在请求上下文中存储登录用户信息
	LoginUserContextKey string = "cas_login_user"
)

// 登录用户字段查询常量
const (
	// queryKeyTicket 票据
	queryKeyTicket = "ticket"

	// queryKeyAgentID 代理ID
	queryKeyAgentID = "agentId"

	// queryKeyRedirectUrl 重定向链接
	queryKeyRedirectUrl = "redirectUrl"

	// queryKeyRedirect 委托登录中的重定向
	queryKeyRedirect = "redirect"

	// queryKeyResourceId 委托登录中的资源 id
	queryKeyResourceId = "resourceId"

	// queryKeyLogoutRequest 登出 saml
	queryKeyLogoutRequest = "logoutRequest"

	// queryKeyEventType 事件类型
	queryKeyEventType = "eventType"

	// queryKeyYunnaoFlag 云脑标志
	queryKeyYunnaoFlag = "yunnaoFlag"

	// queryKeyYunnaoFlagV2 云脑标志V2
	queryKeyYunnaoFlagV2 = "YUNNAOFLAG"
)

// XML 中用户属性字段常量
const (
	// attrSessionTTLMinutes 会话过期时间
	attrSessionTTLMinutes = "sessionTtlMinutes"

	// attrDomainID 账户ID
	attrDomainID = "domainId"

	// attrAccountID 账户ID
	attrAccountID = "accountId"

	// attrUserID 用户ID
	attrUserID = "userId"

	// attrCTYunRootUserID 天翼云根用户ID
	attrCTYunRootUserID = "ctyunRootUserId"

	// attrEmail 邮箱
	attrEmail = "email"

	// attrName 名称
	attrName = "name"

	// attrUserType 用户类型
	attrUserType = "userType"

	// attrRealName 实名
	attrRealName = "realName"

	// attrCity 城市
	attrCity = "city"

	// attrUid 用户ID
	attrUid = "uid"

	// attrDelegate 委托
	attrDelegate = "delegate"

	// attrDelegateCTYunUserID 委托天翼云用户ID
	attrDelegateCTYunUserID = "delegateCtyunUserId"

	// attrDelegateCTYunAcctID 委托天翼云账户ID
	attrDelegateCTYunAcctID = "delegateCtyunAcctId"

	// attrDelegateEmail 委托邮箱
	attrDelegateEmail = "delegateEmail"

	// attrDelegateName 委托名称
	attrDelegateName = "delegateName"

	// delegatePhone 委托电话
	attrDelegatePhone = "delegatePhone"

	// attrState 状态
	attrState = "state"

	// attrActiveState 活跃状态
	attrActiveState = "active"
)
